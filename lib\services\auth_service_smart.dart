import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة الذكية مع Google Sign-In ومحاكاة احتياطية
class AuthService {
  static GoogleSignIn? _googleSignIn;
  static bool _googleSignInAvailable = true;

  // تهيئة Google Sign-In مع معالجة الأخطاء
  static GoogleSignIn? get googleSignIn {
    if (!_googleSignInAvailable) return null;

    if (_googleSignIn == null) {
      try {
        if (kIsWeb) {
          // للويب: تجاهل Google Sign-In الحقيقي مؤقتاً لتجنب مشاكل CLIENT_ID
          if (kDebugMode) {
            print(
              '🌐 الويب: تخطي Google Sign-In الحقيقي، استخدام المحاكاة الذكية',
            );
          }
          _googleSignInAvailable = false;
          return null;
        } else {
          // إعداد للمنصات الأخرى (Android/iOS)
          _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ تعذر تهيئة Google Sign-In: $e');
        }
        _googleSignInAvailable = false;
        return null;
      }
    }
    return _googleSignIn;
  }

  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google الذكي مع محاكاة احتياطية
  static Future<AuthResult> signInWithGoogle() async {
    if (kDebugMode) {
      print('🔄 بدء عملية تسجيل الدخول بـ Google الذكي...');
    }

    // محاولة Google Sign-In الحقيقي أولاً
    if (_googleSignInAvailable && googleSignIn != null) {
      try {
        if (kDebugMode) {
          print('🌐 محاولة Google Sign-In الحقيقي...');
        }

        final GoogleSignInAccount? googleUser = await googleSignIn!.signIn();

        if (googleUser != null) {
          if (kDebugMode) {
            print('✅ نجح Google Sign-In الحقيقي: ${googleUser.email}');
          }

          // الحصول على تفاصيل المصادقة
          final GoogleSignInAuthentication googleAuth =
              await googleUser.authentication;

          // إنشاء مستخدم في النظام المحاكي
          final userData = user_model.User(
            id: 'google_real_${googleUser.id}',
            email: googleUser.email,
            firstName: googleUser.displayName?.split(' ').first ?? 'مستخدم',
            lastName:
                googleUser.displayName?.split(' ').skip(1).join(' ') ??
                'Google',
            phone: null,
            isEmailVerified: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // حفظ المستخدم مع البيانات الحقيقية
          await FirebaseMockService.saveGoogleUser(userData, {
            'googleId': googleUser.id,
            'accessToken': googleAuth.accessToken,
            'idToken': googleAuth.idToken,
            'photoUrl': googleUser.photoUrl,
            'serverAuthCode': googleUser.serverAuthCode,
            'platform': kIsWeb ? 'web_real' : 'mobile_real',
            'signInMethod': 'google_real',
          });

          return AuthResult.success(userData);
        } else {
          if (kDebugMode) {
            print('❌ تم إلغاء تسجيل الدخول من قبل المستخدم');
          }
          return AuthResult.failure('تم إلغاء تسجيل الدخول');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل Google Sign-In الحقيقي: $e');
          print('🔄 التبديل إلى المحاكاة الذكية...');
        }
        _googleSignInAvailable = false;
      }
    }

    // المحاكاة الذكية كبديل
    return await _smartGoogleSignInSimulation();
  }

  /// محاكاة ذكية لـ Google Sign-In
  static Future<AuthResult> _smartGoogleSignInSimulation() async {
    if (kDebugMode) {
      print('🎭 تفعيل محاكاة Google Sign-In الذكية...');
    }

    // قائمة حسابات Google تجريبية واقعية
    final List<Map<String, String>> demoAccounts = [
      {
        'email': '<EMAIL>',
        'firstName': 'أحمد',
        'lastName': 'محمد',
        'photoUrl': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      },
      {
        'email': '<EMAIL>',
        'firstName': 'سارة',
        'lastName': 'علي',
        'photoUrl': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      },
      {
        'email': '<EMAIL>',
        'firstName': 'عمر',
        'lastName': 'حسن',
        'photoUrl': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      },
      {
        'email': '<EMAIL>',
        'firstName': 'فاطمة',
        'lastName': 'إبراهيم',
        'photoUrl': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
      },
    ];

    // اختيار حساب عشوائي
    final selectedAccount =
        demoAccounts[DateTime.now().millisecondsSinceEpoch %
            demoAccounts.length];

    // محاكاة تأخير شبكة واقعي
    await Future.delayed(const Duration(milliseconds: 1500));

    // إنشاء مستخدم بالبيانات المحاكاة
    final userData = user_model.User(
      id: 'google_sim_${DateTime.now().millisecondsSinceEpoch}',
      email: selectedAccount['email']!,
      firstName: selectedAccount['firstName']!,
      lastName: selectedAccount['lastName']!,
      phone: null,
      isEmailVerified: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // حفظ المستخدم مع بيانات المحاكاة
    await FirebaseMockService.saveGoogleUser(userData, {
      'googleId': 'sim_${DateTime.now().millisecondsSinceEpoch}',
      'accessToken':
          'sim_access_token_${DateTime.now().millisecondsSinceEpoch}',
      'idToken': 'sim_id_token_${DateTime.now().millisecondsSinceEpoch}',
      'photoUrl': selectedAccount['photoUrl'],
      'serverAuthCode': null,
      'platform': kIsWeb ? 'web_simulation' : 'mobile_simulation',
      'signInMethod': 'google_simulation',
      'note': 'حساب تجريبي لأغراض التطوير والاختبار',
      'simulationTimestamp': DateTime.now().toIso8601String(),
    });

    if (kDebugMode) {
      print('✅ تم إنشاء حساب Google تجريبي: ${selectedAccount['email']}');
      print(
        '👤 الاسم: ${selectedAccount['firstName']} ${selectedAccount['lastName']}',
      );
    }

    return AuthResult.success(userData);
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      // محاولة تسجيل الخروج من Google إذا كان متاحاً
      if (_googleSignInAvailable && googleSignIn != null) {
        await googleSignIn!.signOut();
      }

      // تسجيل الخروج من النظام المحاكي
      return await FirebaseMockService.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تسجيل الخروج: $e');
      }
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// الحصول على معلومات حساب Google الحالي
  static GoogleSignInAccount? get currentGoogleUser =>
      _googleSignInAvailable && googleSignIn != null
      ? googleSignIn!.currentUser
      : null;

  /// التحقق من تسجيل الدخول بـ Google
  static bool get isSignedInWithGoogle => currentGoogleUser != null;

  /// التحقق من توفر Google Sign-In
  static bool get isGoogleSignInAvailable => _googleSignInAvailable;
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
