import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_simple.dart' as user_model;
import '../firebase_options.dart';

/// خدمة Firebase الحقيقية - مُفعّلة
class FirebaseRealService {
  static FirebaseAuth? _auth;
  static FirebaseFirestore? _firestore;
  static GoogleSignIn? _googleSignIn;
  static bool _isInitialized = false;

  /// طباعة رسالة في Console للويب والموبايل
  static void _logMessage(String message) {
    if (kDebugMode) {
      debugPrint('🔥 Firebase: $message');
    }
  }

  /// تهيئة Firebase
  static Future<void> initialize() async {
    try {
      if (_isInitialized) {
        _logMessage('Firebase مُهيأ مسبقاً');
        return;
      }

      _logMessage('🔥 بدء تهيئة Firebase الحقيقي...');

      try {
        // تهيئة Firebase Core مع التكوين الصحيح
        _logMessage('📱 تهيئة Firebase Core...');
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        _logMessage('✅ تم تهيئة Firebase Core بنجاح');
      } catch (e) {
        _logMessage('❌ خطأ في تهيئة Firebase Core: $e');
        rethrow;
      }

      try {
        // تهيئة الخدمات
        _logMessage('🔧 تهيئة الخدمات...');
        _auth = FirebaseAuth.instance;
        _firestore = FirebaseFirestore.instance;
        _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);
        _logMessage('✅ تم تهيئة الخدمات بنجاح');
      } catch (e) {
        _logMessage('❌ خطأ في تهيئة الخدمات: $e');
        rethrow;
      }

      // انتظار قليل للتأكد من اكتمال التهيئة
      _logMessage('⏳ انتظار اكتمال التهيئة...');
      await Future.delayed(const Duration(milliseconds: 500));

      _isInitialized = true;

      _logMessage('✅ تم تهيئة Firebase الحقيقي بنجاح');
      _logMessage('🔥 Firebase Auth: ${_auth != null ? 'متاح' : 'غير متاح'}');
      _logMessage('🔥 Firestore: ${_firestore != null ? 'متاح' : 'غير متاح'}');
      _logMessage(
        '🔥 Google Sign-In: ${_googleSignIn != null ? 'متاح' : 'غير متاح'}',
      );
      _logMessage('🆔 Project ID: visionlens-app-5ab70');
      _logMessage('🌐 Auth Domain: visionlens-app-5ab70.firebaseapp.com');
      _logMessage(
        '🔗 Firestore URL: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore',
      );

      // اختبار الاتصال بـ Firestore
      try {
        await _firestore!.collection('test').limit(1).get();
        _logMessage('✅ اختبار الاتصال بـ Firestore نجح');
      } catch (e) {
        _logMessage('❌ فشل اختبار الاتصال بـ Firestore: $e');
      }
    } catch (e) {
      _logMessage('❌ خطأ عام في تهيئة Firebase: $e');
      _logMessage('❌ نوع الخطأ: ${e.runtimeType}');
      _logMessage('❌ تفاصيل الخطأ: ${e.toString()}');
      rethrow;
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<user_model.User?> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('🔄 بدء تسجيل الدخول بـ Google...');
      }

      if (!_isInitialized) await initialize();

      if (_googleSignIn == null || _auth == null) {
        throw Exception('Firebase غير مهيأ');
      }

      // تسجيل الدخول بـ Google
      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ تم إلغاء تسجيل الدخول بـ Google');
        }
        return null;
      }

      // الحصول على بيانات المصادقة
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // إنشاء بيانات اعتماد Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول في Firebase
      final UserCredential userCredential = await _auth!.signInWithCredential(
        credential,
      );

      if (userCredential.user != null) {
        // تقسيم الاسم الكامل
        final fullName = googleUser.displayName ?? 'مستخدم';
        final nameParts = fullName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : 'مستخدم';
        final lastName = nameParts.length > 1
            ? nameParts.sublist(1).join(' ')
            : 'جديد';

        // إنشاء نموذج المستخدم
        final user = user_model.User(
          id: userCredential.user!.uid,
          email: userCredential.user!.email ?? googleUser.email,
          firstName: firstName,
          lastName: lastName,
          profileImage: userCredential.user!.photoURL ?? googleUser.photoUrl,
          isEmailVerified: userCredential.user!.emailVerified,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // حفظ المستخدم في Firestore
        await _firestore!
            .collection('users')
            .doc(user.id)
            .set(user.toJson(), SetOptions(merge: true));

        // حفظ بيانات تسجيل الدخول
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_user', jsonEncode(user.toJson()));
        await prefs.setBool('is_logged_in', true);

        if (kDebugMode) {
          print('✅ تم تسجيل الدخول بنجاح!');
          print('📧 البريد الإلكتروني: ${user.email}');
          print('👤 الاسم: ${user.fullName}');
          print('🆔 Firebase UID: ${userCredential.user?.uid}');
          print('🆔 Google ID: ${googleUser.id}');
        }

        return user;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      }
      return null;
    }
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    try {
      if (_auth != null) {
        await _auth!.signOut();
      }
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }

      // مسح بيانات تسجيل الدخول
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      await prefs.setBool('is_logged_in', false);

      if (kDebugMode) {
        print('✅ تم تسجيل الخروج بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الخروج: $e');
      }
    }
  }

  /// الحصول على المستخدم الحالي
  static User? getCurrentUser() {
    return _auth?.currentUser;
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isSignedIn() {
    return _auth?.currentUser != null;
  }

  /// الحصول على Firestore instance
  static FirebaseFirestore? get firestore => _firestore;

  /// الحصول على Auth instance
  static FirebaseAuth? get auth => _auth;

  // ==================== دوال البيانات ====================

  /// جلب المنتجات من Firestore
  static Future<List<Map<String, dynamic>>> getProducts() async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return [];

      final snapshot = await _firestore!.collection('products').get();
      final products = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      _logMessage('📦 تم جلب ${products.length} منتج من Firestore');
      return products;
    } catch (e) {
      _logMessage('❌ خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// إضافة منتج إلى Firestore
  static Future<bool> addProduct(Map<String, dynamic> productData) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('products').add(productData);
      _logMessage('✅ تم إضافة منتج جديد');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج في Firestore
  static Future<bool> updateProduct(String productId, Map<String, dynamic> updates) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('products').doc(productId).update(updates);
      _logMessage('✅ تم تحديث المنتج: $productId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج من Firestore
  static Future<bool> deleteProduct(String productId) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('products').doc(productId).delete();
      _logMessage('✅ تم حذف المنتج: $productId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// جلب الفئات من Firestore
  static Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return [];

      final snapshot = await _firestore!.collection('categories').get();
      final categories = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      _logMessage('📂 تم جلب ${categories.length} فئة من Firestore');
      return categories;
    } catch (e) {
      _logMessage('❌ خطأ في جلب الفئات: $e');
      return [];
    }
  }

  /// إضافة فئة جديدة
  static Future<bool> addCategory(Map<String, dynamic> categoryData) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('categories').add(categoryData);
      _logMessage('✅ تم إضافة فئة جديدة');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الفئة: $e');
      return false;
    }
  }

  /// تحديث فئة
  static Future<bool> updateCategory(String categoryId, Map<String, dynamic> updates) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('categories').doc(categoryId).update(updates);
      _logMessage('✅ تم تحديث الفئة: $categoryId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تحديث الفئة: $e');
      return false;
    }
  }

  /// حذف فئة
  static Future<bool> deleteCategory(String categoryId) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('categories').doc(categoryId).delete();
      _logMessage('✅ تم حذف الفئة: $categoryId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف الفئة: $e');
      return false;
    }
  }

  /// جلب المستخدمين من Firestore
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return [];

      final snapshot = await _firestore!.collection('users').get();
      final users = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      _logMessage('👥 تم جلب ${users.length} مستخدم من Firestore');
      return users;
    } catch (e) {
      _logMessage('❌ خطأ في جلب المستخدمين: $e');
      return [];
    }
  }

  /// جلب الطلبات من Firestore
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return [];

      final snapshot = await _firestore!.collection('orders').get();
      final orders = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      _logMessage('📦 تم جلب ${orders.length} طلب من Firestore');
      return orders;
    } catch (e) {
      _logMessage('❌ خطأ في جلب الطلبات: $e');
      return [];
    }
  }

  /// إضافة طلب جديد
  static Future<bool> addOrder(Map<String, dynamic> orderData) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('orders').add(orderData);
      _logMessage('✅ تم إضافة طلب جديد');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الطلب: $e');
      return false;
    }
  }

  /// جلب البراندات من Firestore
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return [];

      final snapshot = await _firestore!.collection('brands').get();
      final brands = snapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();

      _logMessage('🏷️ تم جلب ${brands.length} براند من Firestore');
      return brands;
    } catch (e) {
      _logMessage('❌ خطأ في جلب البراندات: $e');
      return [];
    }
  }

  /// إضافة براند جديد
  static Future<bool> addBrand(Map<String, dynamic> brandData) async {
    try {
      if (!_isInitialized) await initialize();
      if (_firestore == null) return false;

      await _firestore!.collection('brands').add(brandData);
      _logMessage('✅ تم إضافة براند جديد');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة البراند: $e');
      return false;
    }
  }
}
