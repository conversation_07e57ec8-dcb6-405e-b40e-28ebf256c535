import 'dart:async';
import 'package:flutter/foundation.dart';
import 'models/product.dart';
import 'models/user_simple.dart' as user_model;
import 'models/order.dart' as order_model;
import 'models/category.dart' as category_model;
import 'models/review.dart';
import 'services/firebase_mock_service.dart';

/// خدمة API محاكية للتطوير والاختبار
class ApiService {
  // ==================== المصادقة ====================
  
  static Future<bool> login(String email, String password) async {
    final result = await FirebaseMockService.signInWithEmailAndPassword(
      email: email,
      password: password
    );
    return result.isSuccess;
  }

  static Future<bool> register(String email, String password, String firstName, String lastName) async {
    final result = await FirebaseMockService.createUserWithEmailAndPassword(
      email: email,
      password: password,
      firstName: firstName,
      lastName: lastName
    );
    return result.isSuccess;
  }

  static Future<bool> signInWithGoogle() async {
    final result = await FirebaseMockService.signInWithGoogle();
    return result.isSuccess;
  }

  static Future<bool> forgotPassword(String email) async {
    // محاكاة إرسال إيميل استعادة كلمة المرور
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  static Future<bool> logout() async {
    return await FirebaseMockService.signOut();
  }

  static user_model.User? getCurrentUser() {
    return FirebaseMockService.getCurrentUser();
  }

  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== المنتجات ====================

  static Future<List<Product>> getProducts({String? categoryId, int? limit}) async {
    if (categoryId != null) {
      return await FirebaseMockService.getProductsByCategory(categoryId);
    }
    final products = await FirebaseMockService.getAllProducts();
    if (limit != null && limit > 0) {
      return products.take(limit).toList();
    }
    return products;
  }

  static Future<List<Product>> getFeaturedProducts() async {
    final products = await FirebaseMockService.getAllProducts();
    return products.where((p) => p.isFeatured).toList();
  }

  static Future<Product?> getProductById(String productId) async {
    return await FirebaseMockService.getProductById(productId);
  }

  static Future<List<Product>> searchProducts(String query) async {
    return await FirebaseMockService.searchProducts(query);
  }

  // ==================== الفئات ====================

  static Future<List<category_model.Category>> getCategories() async {
    return await FirebaseMockService.getAllCategories();
  }

  // ==================== الطلبات ====================

  static Future<bool> addOrder(order_model.Order order) async {
    return await FirebaseMockService.addOrder(order);
  }

  static Future<List<order_model.Order>> getOrders() async {
    final user = getCurrentUser();
    if (user != null) {
      return await FirebaseMockService.getUserOrders(user.id);
    }
    return [];
  }

  // ==================== المراجعات ====================

  static Future<List<Review>> getProductReviews(String productId) async {
    return await FirebaseMockService.getProductReviews(productId);
  }

  static Future<bool> addReview(Review review) async {
    return await FirebaseMockService.addReview(review);
  }

  // ==================== الإدارة ====================

  static Future<bool> addProduct(Product product) async {
    return await FirebaseMockService.addProduct(product);
  }

  static Future<bool> updateProduct(Product product) async {
    return await FirebaseMockService.updateProduct(product);
  }

  static Future<bool> deleteProduct(String productId) async {
    return await FirebaseMockService.deleteProduct(productId);
  }

  static Future<bool> addCategory(category_model.Category category) async {
    return await FirebaseMockService.addCategory(category);
  }

  static Future<bool> updateCategory(category_model.Category category) async {
    return await FirebaseMockService.updateCategory(category);
  }

  static Future<bool> deleteCategory(String categoryId) async {
    return await FirebaseMockService.deleteCategory(categoryId);
  }

  // ==================== السلة والمفضلة ====================

  static Future<List<Map<String, dynamic>>> getUserCart(String userId) async {
    return await FirebaseMockService.getUserCart(userId);
  }

  static Future<bool> addToCart(String userId, String productId, int quantity) async {
    final cartItem = {
      'productId': productId,
      'quantity': quantity,
    };
    return await FirebaseMockService.addToCart(userId, cartItem);
  }

  static Future<bool> removeFromCart(String userId, String productId) async {
    return await FirebaseMockService.removeFromCart(userId, productId);
  }

  static Future<bool> clearCart(String userId) async {
    return await FirebaseMockService.clearCart(userId);
  }

  static Future<List<String>> getUserWishlist(String userId) async {
    final wishlist = await FirebaseMockService.getUserWishlist(userId);
    return wishlist.map((item) => item['productId'] as String).toList();
  }

  static Future<bool> addToWishlist(String userId, String productId) async {
    return await FirebaseMockService.addToWishlist(userId, productId);
  }

  static Future<bool> removeFromWishlist(String userId, String productId) async {
    return await FirebaseMockService.removeFromWishlist(userId, productId);
  }

  // ==================== الإحصائيات ====================

  static Map<String, dynamic> getStats() {
    return FirebaseMockService.getStats();
  }

  // ==================== التهيئة ====================

  static Future<void> initialize() async {
    await FirebaseMockService.initialize();
    if (kDebugMode) {
      print('✅ تم تهيئة ApiService بنجاح');
    }
  }
}
