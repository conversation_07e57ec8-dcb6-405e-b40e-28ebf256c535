import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/review.dart';
import '../../services/firebase_web_service.dart';
import '../../api_service_mock.dart';

class ReviewsPage extends StatefulWidget {
  final Product product;

  const ReviewsPage({super.key, required this.product});

  @override
  State<ReviewsPage> createState() => _ReviewsPageState();
}

class _ReviewsPageState extends State<ReviewsPage> {
  List<Review> _reviews = [];
  bool _isLoading = true;
  bool _showAddReview = false;
  
  // متحكمات النموذج
  final _reviewController = TextEditingController();
  final _nameController = TextEditingController();
  double _rating = 5.0;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _loadReviews();
  }

  @override
  void dispose() {
    _reviewController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadReviews() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Review> reviews = [];
      
      // محاولة جلب التقييمات من Firebase
      try {
        reviews = await FirebaseWebService.getProductReviews(widget.product.id);
      } catch (e) {
        if (kDebugMode) {
          print('فشل في جلب التقييمات من Firebase: $e');
        }
        // استخدام البيانات المحلية كبديل
        reviews = await ApiService.getProductReviews(widget.product.id);
      }

      if (mounted) {
        setState(() {
          _reviews = reviews;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _reviews = [];
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقييمات ${widget.product.name}'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // ملخص التقييمات
                _buildRatingSummary(),
                
                // قائمة التقييمات
                Expanded(
                  child: _reviews.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                          itemCount: _reviews.length,
                          itemBuilder: (context, index) {
                            return _buildReviewCard(_reviews[index]);
                          },
                        ),
                ),
                
                // نموذج إضافة تقييم
                if (_showAddReview) _buildAddReviewForm(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          setState(() {
            _showAddReview = !_showAddReview;
          });
        },
        icon: Icon(_showAddReview ? Icons.close : Icons.add),
        label: Text(_showAddReview ? 'إلغاء' : 'إضافة تقييم'),
        backgroundColor: AppColors.primaryColor,
      ),
    );
  }

  Widget _buildRatingSummary() {
    final averageRating = widget.product.rating;
    final totalReviews = _reviews.length;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // التقييم العام
          Column(
            children: [
              Text(
                averageRating.toStringAsFixed(1),
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
              RatingBarIndicator(
                rating: averageRating,
                itemBuilder: (context, index) => const Icon(
                  Icons.star,
                  color: AppColors.frameGold,
                ),
                itemCount: 5,
                itemSize: 20.0,
              ),
              const SizedBox(height: 4),
              Text(
                '$totalReviews تقييم',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.secondaryText,
                ),
              ),
            ],
          ),
          
          const SizedBox(width: 24),
          
          // توزيع النجوم
          Expanded(
            child: Column(
              children: List.generate(5, (index) {
                final starCount = 5 - index;
                final count = _reviews.where((r) => r.rating.round() == starCount).length;
                final percentage = totalReviews > 0 ? count / totalReviews : 0.0;
                
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      Text('$starCount'),
                      const SizedBox(width: 4),
                      const Icon(Icons.star, size: 16, color: AppColors.frameGold),
                      const SizedBox(width: 8),
                      Expanded(
                        child: LinearProgressIndicator(
                          value: percentage,
                          backgroundColor: AppColors.lightGrey,
                          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.frameGold),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text('$count'),
                    ],
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.rate_review_outlined,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تقييمات بعد',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يقيم هذا المنتج!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(Review review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المراجع
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppColors.primaryColor,
                child: Text(
                  review.userName.isNotEmpty ? review.userName[0].toUpperCase() : 'م',
                  style: const TextStyle(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.userName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _formatDate(review.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),
              RatingBarIndicator(
                rating: review.rating,
                itemBuilder: (context, index) => const Icon(
                  Icons.star,
                  color: AppColors.frameGold,
                ),
                itemCount: 5,
                itemSize: 16.0,
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // نص التقييم
          Text(
            review.comment,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildAddReviewForm() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'إضافة تقييم',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // اسم المراجع
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسمك',
              border: OutlineInputBorder(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // التقييم
          Row(
            children: [
              const Text('التقييم: '),
              RatingBar.builder(
                initialRating: _rating,
                minRating: 1,
                direction: Axis.horizontal,
                allowHalfRating: false,
                itemCount: 5,
                itemSize: 30,
                itemBuilder: (context, _) => const Icon(
                  Icons.star,
                  color: AppColors.frameGold,
                ),
                onRatingUpdate: (rating) {
                  setState(() {
                    _rating = rating;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // نص التقييم
          TextField(
            controller: _reviewController,
            decoration: const InputDecoration(
              labelText: 'اكتب تقييمك',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          
          const SizedBox(height: 16),
          
          // زر الإرسال
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _submitReview,
              child: _isSubmitting
                  ? const CircularProgressIndicator(color: AppColors.white)
                  : const Text('إرسال التقييم'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _submitReview() async {
    if (_nameController.text.trim().isEmpty || _reviewController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final review = Review(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: widget.product.id,
        userId: 'anonymous',
        userName: _nameController.text.trim(),
        rating: _rating,
        comment: _reviewController.text.trim(),
        createdAt: DateTime.now(),
      );

      // حفظ التقييم
      bool success = false;
      try {
        success = await FirebaseWebService.addReview(review);
      } catch (e) {
        await ApiService.addReview(review);
        success = true;
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة تقييمك بنجاح!'),
            backgroundColor: AppColors.successColor,
          ),
        );

        // إعادة تحميل التقييمات
        _loadReviews();
        
        // إخفاء النموذج وتنظيف الحقول
        setState(() {
          _showAddReview = false;
          _nameController.clear();
          _reviewController.clear();
          _rating = 5.0;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إضافة التقييم: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
