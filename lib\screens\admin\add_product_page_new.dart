import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../api_service_mock.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _materialController = TextEditingController();
  final _colorController = TextEditingController();
  final _sizeController = TextEditingController();

  String _selectedCategory = 'eyeglasses';
  ProductType _selectedType = ProductType.eyeglasses;
  bool _isLoading = false;
  bool _isFeatured = false;
  bool _isOnSale = false;
  bool _isNew = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // معلومات أساسية
              _buildBasicInfoCard(),
              const SizedBox(height: 16),

              // معلومات السعر والمخزون
              _buildPriceStockCard(),
              const SizedBox(height: 16),

              // تفاصيل المنتج
              _buildProductDetailsCard(),
              const SizedBox(height: 16),

              // إعدادات المنتج
              _buildProductSettingsCard(),
              const SizedBox(height: 24),

              // زر الحفظ
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج *',
                hintText: 'مثال: نظارة VisionLens الطبية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.shopping_bag),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم المنتج';
                }
                if (value.length < 3) {
                  return 'اسم المنتج يجب أن يكون 3 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج *',
                hintText: 'اكتب وصفاً مفصلاً للمنتج...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال وصف المنتج';
                }
                if (value.length < 10) {
                  return 'الوصف يجب أن يكون 10 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'فئة المنتج *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'eyeglasses',
                  child: Text('نظارات طبية'),
                ),
                DropdownMenuItem(
                  value: 'sunglasses',
                  child: Text('نظارات شمسية'),
                ),
                DropdownMenuItem(
                  value: 'contact-lenses',
                  child: Text('عدسات لاصقة'),
                ),
                DropdownMenuItem(
                  value: 'reading-glasses',
                  child: Text('نظارات قراءة'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                  _selectedType = _getProductType(value);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceStockCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'السعر والمخزون',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر الحالي (د.ع) *',
                      hintText: '75000',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال السعر';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (double.parse(value) <= 0) {
                        return 'السعر يجب أن يكون أكبر من صفر';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _originalPriceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر الأصلي (د.ع)',
                      hintText: '100000',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.price_change),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        final currentPrice = double.tryParse(
                          _priceController.text,
                        );
                        final originalPrice = double.parse(value);
                        if (currentPrice != null &&
                            originalPrice <= currentPrice) {
                          return 'السعر الأصلي يجب أن يكون أكبر من السعر الحالي';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _stockController,
              decoration: const InputDecoration(
                labelText: 'الكمية المتوفرة *',
                hintText: '50',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.inventory),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الكمية';
                }
                if (int.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                if (int.parse(value) < 0) {
                  return 'الكمية لا يمكن أن تكون سالبة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetailsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل المنتج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _brandController,
                    decoration: const InputDecoration(
                      labelText: 'العلامة التجارية',
                      hintText: 'VisionLens',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.branding_watermark),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _materialController,
                    decoration: const InputDecoration(
                      labelText: 'المادة',
                      hintText: 'أسيتات',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.texture),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _colorController,
                    decoration: const InputDecoration(
                      labelText: 'اللون',
                      hintText: 'أسود',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.color_lens),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _sizeController,
                    decoration: const InputDecoration(
                      labelText: 'المقاس',
                      hintText: 'متوسط',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.straighten),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات المنتج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('منتج جديد'),
              subtitle: const Text('سيظهر في قسم المنتجات الجديدة'),
              value: _isNew,
              onChanged: (value) {
                setState(() {
                  _isNew = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),

            SwitchListTile(
              title: const Text('منتج مميز'),
              subtitle: const Text('سيظهر في قسم المنتجات المميزة'),
              value: _isFeatured,
              onChanged: (value) {
                setState(() {
                  _isFeatured = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),

            SwitchListTile(
              title: const Text('عرض خاص'),
              subtitle: const Text('سيظهر في قسم العروض الخاصة'),
              value: _isOnSale,
              onChanged: (value) {
                setState(() {
                  _isOnSale = value;
                });
              },
              activeColor: AppColors.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                ),
              )
            : const Text(
                'حفظ المنتج',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
      ),
    );
  }

  ProductType _getProductType(String categoryId) {
    switch (categoryId) {
      case 'sunglasses':
        return ProductType.sunglasses;
      case 'contact-lenses':
        return ProductType.contactLenses;
      case 'reading-glasses':
        return ProductType.readingGlasses;
      default:
        return ProductType.eyeglasses;
    }
  }

  String _getCategoryName(String categoryId) {
    switch (categoryId) {
      case 'sunglasses':
        return 'نظارات شمسية';
      case 'contact-lenses':
        return 'عدسات لاصقة';
      case 'reading-glasses':
        return 'نظارات قراءة';
      default:
        return 'نظارات طبية';
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final product = Product(
        id: 'product_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text,
        description: _descriptionController.text,
        price: double.parse(_priceController.text),
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        image: 'assets/images/placeholder_product.jpg',
        images: ['assets/images/placeholder_product.jpg'],
        categoryId: _selectedCategory,
        categoryName: _getCategoryName(_selectedCategory),
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {
          'العلامة التجارية': _brandController.text.isNotEmpty
              ? _brandController.text
              : 'VisionLens',
          'المادة': _materialController.text.isNotEmpty
              ? _materialController.text
              : 'غير محدد',
          'اللون': _colorController.text.isNotEmpty
              ? _colorController.text
              : 'غير محدد',
          'المقاس': _sizeController.text.isNotEmpty
              ? _sizeController.text
              : 'غير محدد',
        },
        type: _selectedType,
        brand: _brandController.text.isNotEmpty
            ? _brandController.text
            : 'VisionLens',
        material: _materialController.text.isNotEmpty
            ? _materialController.text
            : 'غير محدد',
        color: _colorController.text.isNotEmpty
            ? _colorController.text
            : 'غير محدد',
        size: _sizeController.text.isNotEmpty
            ? _sizeController.text
            : 'غير محدد',
        availableColors: _colorController.text.isNotEmpty
            ? [_colorController.text]
            : ['أسود'],
        availableSizes: _sizeController.text.isNotEmpty
            ? [_sizeController.text]
            : ['متوسط'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isNew: _isNew,
        isFeatured: _isFeatured,
        isOnSale: _isOnSale,
        discountPercentage:
            _isOnSale && _originalPriceController.text.isNotEmpty
            ? ((double.parse(_originalPriceController.text) -
                      double.parse(_priceController.text)) /
                  double.parse(_originalPriceController.text) *
                  100)
            : null,
      );

      await ApiService.addProduct(product);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ المنتج بنجاح'),
            backgroundColor: AppColors.successColor,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ المنتج: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _materialController.dispose();
    _colorController.dispose();
    _sizeController.dispose();
    super.dispose();
  }
}
