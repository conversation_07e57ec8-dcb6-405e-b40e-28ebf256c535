// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDvOiEU9Q8W7X2Y3Z4A5B6C7D8E9F0G1H2',
    appId: '1:123456789012:web:abcdef123456789012',
    messagingSenderId: '123456789012',
    projectId: 'visionlens-app-real',
    authDomain: 'visionlens-app-real.firebaseapp.com',
    storageBucket: 'visionlens-app-real.appspot.com',
    measurementId: 'G-ABCDEF1234',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDemo_API_Key_For_Android',
    appId: '1:123456789:android:demo_app_id',
    messagingSenderId: '123456789',
    projectId: 'visionlens-demo',
    storageBucket: 'visionlens-demo.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDemo_API_Key_For_iOS',
    appId: '1:123456789:ios:demo_app_id',
    messagingSenderId: '123456789',
    projectId: 'visionlens-demo',
    storageBucket: 'visionlens-demo.appspot.com',
    iosBundleId: 'com.visionlens.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDemo_API_Key_For_macOS',
    appId: '1:123456789:macos:demo_app_id',
    messagingSenderId: '123456789',
    projectId: 'visionlens-demo',
    storageBucket: 'visionlens-demo.appspot.com',
    iosBundleId: 'com.visionlens.app',
  );
}
