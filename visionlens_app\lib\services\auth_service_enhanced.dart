import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة المحسنة مع Google Sign-In الحقيقي
class AuthService {
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId:
        '123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
    scopes: ['email', 'profile'],
  );

  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google الحقيقي
  static Future<AuthResult> signInWithGoogle() async {
    try {
      print('🔄 بدء عملية تسجيل الدخول بـ Google الحقيقي...');

      // بدء عملية تسجيل الدخول الحقيقية بـ Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        print('❌ تم إلغاء تسجيل الدخول من قبل المستخدم');
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      print('✅ تم اختيار حساب Google الحقيقي: ${googleUser.email}');

      // الحصول على تفاصيل المصادقة الحقيقية
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        print('❌ فشل في الحصول على رموز المصادقة');
        return AuthResult.failure('فشل في الحصول على رموز المصادقة');
      }

      print('✅ تم الحصول على رموز المصادقة الحقيقية');
      print('📧 البريد الإلكتروني: ${googleUser.email}');
      print('👤 الاسم: ${googleUser.displayName}');
      print('🖼️ الصورة: ${googleUser.photoUrl}');

      // إنشاء مستخدم في النظام المحاكي باستخدام البيانات الحقيقية
      final userData = user_model.User(
        id: 'google_${googleUser.id}',
        email: googleUser.email,
        firstName: googleUser.displayName?.split(' ').first ?? 'مستخدم',
        lastName:
            googleUser.displayName?.split(' ').skip(1).join(' ') ?? 'Google',
        phone: null,
        isEmailVerified: true, // حسابات Google محققة دائماً
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المستخدم في النظام المحاكي
      await FirebaseMockService.saveGoogleUser(userData, {
        'googleId': googleUser.id,
        'accessToken': googleAuth.accessToken,
        'idToken': googleAuth.idToken,
        'photoUrl': googleUser.photoUrl,
        'serverAuthCode': googleUser.serverAuthCode,
      });

      print('✅ تم حفظ بيانات المستخدم في النظام المحاكي');

      return AuthResult.success(userData);
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      return AuthResult.failure('حدث خطأ في تسجيل الدخول بـ Google: $e');
    }
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      // تسجيل الخروج من Google أيضاً
      await _googleSignIn.signOut();

      // تسجيل الخروج من النظام المحاكي
      return await FirebaseMockService.signOut();
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    try {
      // محاكاة إرسال رسالة التأكيد
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      print('خطأ في إرسال رسالة التأكيد: $e');
      return false;
    }
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      // محاكاة إرسال رسالة إعادة التعيين
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      print('خطأ في إرسال رسالة إعادة التعيين: $e');
      return false;
    }
  }

  /// تغيير كلمة المرور (محاكاة)
  static Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // محاكاة تغيير كلمة المرور
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      print('خطأ في تغيير كلمة المرور: $e');
      return false;
    }
  }

  /// تحديث البريد الإلكتروني (محاكاة)
  static Future<bool> updateEmail(String newEmail) async {
    try {
      // محاكاة تحديث البريد الإلكتروني
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      print('خطأ في تحديث البريد الإلكتروني: $e');
      return false;
    }
  }

  /// حذف الحساب (محاكاة)
  static Future<bool> deleteAccount(String password) async {
    try {
      // محاكاة حذف الحساب
      await Future.delayed(const Duration(seconds: 1));
      await signOut();
      return true;
    } catch (e) {
      print('خطأ في حذف الحساب: $e');
      return false;
    }
  }

  /// الحصول على معلومات حساب Google الحالي
  static GoogleSignInAccount? get currentGoogleUser =>
      _googleSignIn.currentUser;

  /// التحقق من تسجيل الدخول بـ Google
  static bool get isSignedInWithGoogle => _googleSignIn.currentUser != null;

  /// الحصول على رسالة خطأ مفهومة
  static String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'requires-recent-login':
        return 'يتطلب تسجيل دخول حديث';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بنفس البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'credential-already-in-use':
        return 'بيانات الاعتماد مستخدمة بالفعل';
      default:
        return 'حدث خطأ غير متوقع: $errorCode';
    }
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
