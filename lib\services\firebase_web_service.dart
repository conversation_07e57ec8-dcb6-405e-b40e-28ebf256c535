import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:js' as js;
import '../models/product.dart';
import '../models/review.dart';

/// خدمة Firebase للويب - تستخدم Firebase JavaScript SDK
class FirebaseWebService {
  static bool _isInitialized = false;

  static void _logMessage(String message) {
    if (kDebugMode) {
      debugPrint('Firebase Web: $message');
    }
  }

  static Future<bool> initialize() async {
    if (!kIsWeb) {
      _logMessage('هذه الخدمة تعمل فقط في Web');
      return false;
    }

    try {
      // التحقق من وجود Firebase في الصفحة
      if (js.context.hasProperty('firebase')) {
        _logMessage('Firebase JavaScript SDK متاح');
        _isInitialized = true;
        return true;
      } else {
        _logMessage('Firebase JavaScript SDK غير متاح');
        return false;
      }
    } catch (e) {
      _logMessage('خطأ في تهيئة Firebase: $e');
      return false;
    }
  }

  static Future<List<Product>> getProducts() async {
    if (!kIsWeb || !_isInitialized) return [];

    try {
      _logMessage('جلب المنتجات من Firestore...');

      // استخدام طريقة محسنة لجلب المنتجات مع Promise handling
      _logMessage('محاولة جلب المنتجات من Firestore...');

      final completer = Completer<List<Product>>();

      // إنشاء callback function فريد
      final callbackName = 'flutterGetProductsCallback_${DateTime.now().millisecondsSinceEpoch}';
      js.context[callbackName] = js.allowInterop((String jsonData) {
        try {
          final List<dynamic> result = js.context['JSON'].callMethod('parse', [jsonData]);
          final products = <Product>[];

          for (final item in result) {
            Map<String, dynamic> productMap;
            if (item is Map) {
              productMap = Map<String, dynamic>.from(item);
            } else {
              // تحويل JsObject إلى Map
              productMap = <String, dynamic>{};
              final jsObj = item as js.JsObject;
              final keys = js.context['Object'].callMethod('keys', [jsObj]);
              for (int i = 0; i < keys['length']; i++) {
                final key = keys[i];
                productMap[key] = jsObj[key];
              }
            }
            products.add(Product.fromJson(productMap));
          }

          _logMessage('✅ تم جلب ${products.length} منتج من Firestore');
          if (!completer.isCompleted) {
            completer.complete(products);
          }
        } catch (e) {
          _logMessage('❌ خطأ في تحويل بيانات المنتجات: $e');
          if (!completer.isCompleted) {
            completer.complete([]);
          }
        }
      });

      // تنفيذ JavaScript مع callback
      js.context.callMethod('eval', ['''
        firebase.firestore().collection('products').get()
          .then((snapshot) => {
            const products = [];
            snapshot.forEach(doc => {
              products.push({id: doc.id, ...doc.data()});
            });
            console.log('🔥 Firebase: تم جلب', products.length, 'منتج');
            window.$callbackName(JSON.stringify(products));
          })
          .catch((error) => {
            console.error('❌ Firebase: خطأ في جلب المنتجات:', error);
            window.$callbackName(JSON.stringify([]));
          });
      ''']);

      // انتظار النتيجة مع timeout
      try {
        return await completer.future.timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            _logMessage('⏰ انتهت مهلة جلب المنتجات');
            return <Product>[];
          },
        );
      } finally {
        // تنظيف callback
        js.context.deleteProperty(callbackName);
      }

    } catch (e) {
      _logMessage('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  static Future<bool> addProduct(Product product) async {
    if (!kIsWeb || !_isInitialized) return false;

    try {
      _logMessage('إضافة منتج جديد إلى Firestore...');

      // تحويل المنتج إلى JSON
      final productData = product.toJson();

      // إضافة المنتج إلى Firestore باستخدام JavaScript مع timestamp صحيح
      js.context.callMethod('eval', ['''
        (function() {
          console.log('🔄 بدء إضافة المنتج...');
          const productData = ${js.context['JSON'].callMethod('stringify', [productData])};
          console.log('📝 بيانات المنتج:', productData);

          // إضافة timestamp صحيح
          productData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
          productData.updatedAt = firebase.firestore.FieldValue.serverTimestamp();

          console.log('🔥 محاولة الكتابة إلى Firestore...');
          firebase.firestore().collection('products').doc(productData.id).set(productData)
            .then(() => {
              console.log('✅ تم إضافة المنتج بنجاح إلى Firestore:', productData.name);
              window.visionLensLastAddResult = true;
            })
            .catch((error) => {
              console.error('❌ خطأ في إضافة المنتج إلى Firestore:', error);
              console.error('❌ نوع الخطأ:', error.code);
              console.error('❌ رسالة الخطأ:', error.message);
              console.error('❌ تفاصيل كاملة:', error);
              window.visionLensLastAddResult = false;
            });
        })()
      ''']);

      // انتظار قصير للتأكد من الإضافة
      await Future.delayed(const Duration(milliseconds: 2000));

      // قراءة النتيجة
      final success = js.context['visionLensLastAddResult'] == true;

      if (success) {
        _logMessage('✅ تم إضافة المنتج بنجاح إلى Firestore: ${product.name}');
      } else {
        _logMessage('❌ فشل في إضافة المنتج إلى Firestore: ${product.name}');
      }

      return success;

    } catch (e) {
      _logMessage('خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  static Future<bool> updateProduct(Product product) async {
    if (!kIsWeb) return false;
    _logMessage('Firebase Web Service غير مُفعل في هذه المنصة');
    return false;
  }

  static Future<bool> deleteProduct(String productId) async {
    if (!kIsWeb) return false;
    _logMessage('Firebase Web Service غير مُفعل في هذه المنصة');
    return false;
  }

  static Future<List<Map<String, dynamic>>> getCategories() async {
    if (!kIsWeb || !_isInitialized) return [];

    try {
      _logMessage('جلب الفئات من Firestore...');

      // استخدام طريقة محسنة لجلب الفئات مع Promise handling
      _logMessage('محاولة جلب الفئات من Firestore...');

      final completer = Completer<List<Map<String, dynamic>>>();

      // إنشاء callback function فريد
      final callbackName = 'flutterGetCategoriesCallback_${DateTime.now().millisecondsSinceEpoch}';
      js.context[callbackName] = js.allowInterop((String jsonData) {
        try {
          final List<dynamic> result = js.context['JSON'].callMethod('parse', [jsonData]);
          final categories = <Map<String, dynamic>>[];

          for (final item in result) {
            if (item is Map) {
              categories.add(Map<String, dynamic>.from(item));
            } else {
              // تحويل JsObject إلى Map
              final map = <String, dynamic>{};
              final jsObj = item as js.JsObject;
              final keys = js.context['Object'].callMethod('keys', [jsObj]);
              for (int i = 0; i < keys['length']; i++) {
                final key = keys[i];
                map[key] = jsObj[key];
              }
              categories.add(map);
            }
          }

          _logMessage('✅ تم جلب ${categories.length} فئة من Firestore');
          if (!completer.isCompleted) {
            completer.complete(categories);
          }
        } catch (e) {
          _logMessage('❌ خطأ في تحويل بيانات الفئات: $e');
          if (!completer.isCompleted) {
            completer.complete([]);
          }
        }
      });

      // تنفيذ JavaScript مع callback
      js.context.callMethod('eval', ['''
        firebase.firestore().collection('categories').get()
          .then((snapshot) => {
            const categories = [];
            snapshot.forEach(doc => {
              categories.push({id: doc.id, ...doc.data()});
            });
            console.log('🔥 Firebase: تم جلب', categories.length, 'فئة');
            window.$callbackName(JSON.stringify(categories));
          })
          .catch((error) => {
            console.error('❌ Firebase: خطأ في جلب الفئات:', error);
            window.$callbackName(JSON.stringify([]));
          });
      ''']);

      // انتظار النتيجة مع timeout
      try {
        return await completer.future.timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            _logMessage('⏰ انتهت مهلة جلب الفئات');
            return <Map<String, dynamic>>[];
          },
        );
      } finally {
        // تنظيف callback
        js.context.deleteProperty(callbackName);
      }

    } catch (e) {
      _logMessage('خطأ في جلب الفئات: $e');
      return [];
    }
  }

  static Future<bool> addCategory(Map<String, dynamic> categoryData) async {
    if (!kIsWeb || !_isInitialized) return false;

    try {
      _logMessage('إضافة فئة جديدة إلى Firestore...');

      // إضافة الفئة إلى Firestore باستخدام JavaScript مع timestamp صحيح
      js.context.callMethod('eval', ['''
        (function() {
          console.log('🔄 بدء إضافة الفئة...');
          const categoryData = ${js.context['JSON'].callMethod('stringify', [categoryData])};
          console.log('📝 بيانات الفئة:', categoryData);

          // إضافة timestamp صحيح
          categoryData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
          categoryData.updatedAt = firebase.firestore.FieldValue.serverTimestamp();

          console.log('🔥 محاولة الكتابة إلى Firestore...');
          firebase.firestore().collection('categories').doc(categoryData.id).set(categoryData)
            .then(() => {
              console.log('✅ تم إضافة الفئة بنجاح إلى Firestore:', categoryData.name);
              window.visionLensLastCategoryAddResult = true;
            })
            .catch((error) => {
              console.error('❌ خطأ في إضافة الفئة إلى Firestore:', error);
              console.error('❌ نوع الخطأ:', error.code);
              console.error('❌ رسالة الخطأ:', error.message);
              console.error('❌ تفاصيل كاملة:', error);
              window.visionLensLastCategoryAddResult = false;
            });
        })()
      ''']);

      // انتظار قصير للتأكد من الإضافة
      await Future.delayed(const Duration(milliseconds: 2000));

      // قراءة النتيجة
      final success = js.context['visionLensLastCategoryAddResult'] == true;

      if (success) {
        _logMessage('✅ تم إضافة الفئة بنجاح إلى Firestore: ${categoryData['name']}');
      } else {
        _logMessage('❌ فشل في إضافة الفئة إلى Firestore: ${categoryData['name']}');
      }

      return success;

    } catch (e) {
      _logMessage('خطأ في إضافة الفئة: $e');
      return false;
    }
  }

  static Future<bool> updateCategory(Map<String, dynamic> categoryData) async {
    if (!kIsWeb) return false;
    _logMessage('Firebase Web Service غير مُفعل في هذه المنصة');
    return false;
  }

  static Future<bool> deleteCategory(String categoryId) async {
    if (!kIsWeb) return false;
    _logMessage('Firebase Web Service غير مُفعل في هذه المنصة');
    return false;
  }

  static Future<List<Map<String, dynamic>>> getUsers() async {
    if (!kIsWeb) return [];
    _logMessage('Firebase Web Service غير مُفعل في هذه المنصة');
    return [];
  }

  static Future<bool> addUser(Map<String, dynamic> userData) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> updateUser(Map<String, dynamic> userData) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> deleteUser(String userId) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<List<Map<String, dynamic>>> getOrders() async {
    if (!kIsWeb) return [];
    return [];
  }

  static Future<bool> addOrder(Map<String, dynamic> orderData) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> updateOrder(Map<String, dynamic> orderData) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> updateOrderStatus(String orderId, String status) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> deleteOrder(String orderId) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<List<Map<String, dynamic>>> getBrands() async {
    if (!kIsWeb || !_isInitialized) return [];

    try {
      _logMessage('جلب البراندات من Firestore...');

      // استخدام طريقة محسنة لجلب البراندات مع Promise handling
      _logMessage('محاولة جلب البراندات من Firestore...');

      final completer = Completer<List<Map<String, dynamic>>>();

      // إنشاء callback function فريد
      final callbackName = 'flutterGetBrandsCallback_${DateTime.now().millisecondsSinceEpoch}';
      js.context[callbackName] = js.allowInterop((String jsonData) {
        try {
          final List<dynamic> result = js.context['JSON'].callMethod('parse', [jsonData]);
          final brands = <Map<String, dynamic>>[];

          for (final item in result) {
            if (item is Map) {
              brands.add(Map<String, dynamic>.from(item));
            } else {
              // تحويل JsObject إلى Map
              final map = <String, dynamic>{};
              final jsObj = item as js.JsObject;
              final keys = js.context['Object'].callMethod('keys', [jsObj]);
              for (int i = 0; i < keys['length']; i++) {
                final key = keys[i];
                map[key] = jsObj[key];
              }
              brands.add(map);
            }
          }

          _logMessage('✅ تم جلب ${brands.length} براند من Firestore');
          if (!completer.isCompleted) {
            completer.complete(brands);
          }
        } catch (e) {
          _logMessage('❌ خطأ في تحويل بيانات البراندات: $e');
          if (!completer.isCompleted) {
            completer.complete([]);
          }
        }
      });

      // تنفيذ JavaScript مع callback
      js.context.callMethod('eval', ['''
        firebase.firestore().collection('brands').get()
          .then((snapshot) => {
            const brands = [];
            snapshot.forEach(doc => {
              brands.push({id: doc.id, ...doc.data()});
            });
            console.log('🔥 Firebase: تم جلب', brands.length, 'براند');
            window.$callbackName(JSON.stringify(brands));
          })
          .catch((error) => {
            console.error('❌ Firebase: خطأ في جلب البراندات:', error);
            window.$callbackName(JSON.stringify([]));
          });
      ''']);

      // انتظار النتيجة مع timeout
      try {
        return await completer.future.timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            _logMessage('⏰ انتهت مهلة جلب البراندات');
            return <Map<String, dynamic>>[];
          },
        );
      } finally {
        // تنظيف callback
        js.context.deleteProperty(callbackName);
      }

    } catch (e) {
      _logMessage('خطأ في جلب البراندات: $e');
      return [];
    }
  }

  static Future<bool> addBrand(Map<String, dynamic> brandData) async {
    if (!kIsWeb || !_isInitialized) return false;

    try {
      _logMessage('إضافة براند جديد إلى Firestore...');

      // إضافة البراند إلى Firestore باستخدام JavaScript مع timestamp صحيح
      js.context.callMethod('eval', ['''
        (function() {
          console.log('🔄 بدء إضافة البراند...');
          const brandData = ${js.context['JSON'].callMethod('stringify', [brandData])};
          console.log('📝 بيانات البراند:', brandData);

          // إضافة timestamp صحيح
          brandData.createdAt = firebase.firestore.FieldValue.serverTimestamp();
          brandData.updatedAt = firebase.firestore.FieldValue.serverTimestamp();

          console.log('🔥 محاولة الكتابة إلى Firestore...');
          firebase.firestore().collection('brands').doc(brandData.id).set(brandData)
            .then(() => {
              console.log('✅ تم إضافة البراند بنجاح إلى Firestore:', brandData.name);
              window.visionLensLastBrandAddResult = true;
            })
            .catch((error) => {
              console.error('❌ خطأ في إضافة البراند إلى Firestore:', error);
              console.error('❌ نوع الخطأ:', error.code);
              console.error('❌ رسالة الخطأ:', error.message);
              console.error('❌ تفاصيل كاملة:', error);
              window.visionLensLastBrandAddResult = false;
            });
        })()
      ''']);

      // انتظار قصير للتأكد من الإضافة
      await Future.delayed(const Duration(milliseconds: 2000));

      // قراءة النتيجة
      final success = js.context['visionLensLastBrandAddResult'] == true;

      if (success) {
        _logMessage('✅ تم إضافة البراند بنجاح إلى Firestore: ${brandData['name']}');
      } else {
        _logMessage('❌ فشل في إضافة البراند إلى Firestore: ${brandData['name']}');
      }

      return success;

    } catch (e) {
      _logMessage('خطأ في إضافة البراند: $e');
      return false;
    }
  }

  static Future<bool> updateBrand(Map<String, dynamic> brandData) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> deleteBrand(String brandId) async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<bool> addFamousBrands() async {
    if (!kIsWeb) return false;
    return false;
  }

  static Future<List<Review>> getReviews() async {
    if (!kIsWeb) return [];
    return [];
  }

  static Future<List<Review>> getProductReviews(String productId) async {
    if (!kIsWeb) return [];
    return [];
  }

  static Future<bool> addReview(Review review) async {
    if (!kIsWeb) return false;
    return false;
  }
}