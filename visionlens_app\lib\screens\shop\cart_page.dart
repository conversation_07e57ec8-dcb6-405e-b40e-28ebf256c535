import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';
import 'checkout_page.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final AppState _appState = AppState();

  @override
  void initState() {
    super.initState();
    _loadCartData();
  }

  Future<void> _loadCartData() async {
    // تحميل السلة من التخزين المحلي
    final savedCart = await StorageService.loadCart();
    for (final item in savedCart) {
      _appState.addToCart(
        item.product,
        color: item.selectedColor,
        size: item.selectedSize,
        quantity: item.quantity,
      );
    }
  }

  Future<void> _saveCartData() async {
    await StorageService.saveCart(_appState.cartItems);
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _appState,
      builder: (context, child) {
        final cartItems = _appState.cartItems;

        return Scaffold(
          backgroundColor: AppColors.scaffoldBackground,
          appBar: AppBar(
            title: Text('السلة (${cartItems.length})'),
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.primaryText,
            elevation: 1,
            centerTitle: true,
            actions: [
              if (cartItems.isNotEmpty)
                TextButton(
                  onPressed: _clearCart,
                  child: const Text('مسح الكل'),
                ),
            ],
          ),
          body: cartItems.isEmpty ? _buildEmptyCart() : _buildCartContent(),
          bottomNavigationBar: cartItems.isNotEmpty
              ? _buildCheckoutBar()
              : null,
        );
      },
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 100,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'السلة فارغة',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 12),
          Text(
            'أضف منتجات لتبدأ التسوق',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              // العودة للصفحة الرئيسية
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            child: const Text('تصفح المنتجات'),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    final cartItems = _appState.cartItems;

    return Column(
      children: [
        // عناصر السلة
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            itemCount: cartItems.length,
            itemBuilder: (context, index) {
              return _buildCartItemCard(cartItems[index], index);
            },
          ),
        ),

        // ملخص الطلب
        _buildOrderSummary(),
      ],
    );
  }

  Widget _buildCartItemCard(CartItem item, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusMedium,
              ),
            ),
            child: Icon(
              Icons.visibility,
              size: 30,
              color: AppColors.grey.withValues(alpha: 0.5),
            ),
          ),

          const SizedBox(width: 16),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (item.selectedColor != null || item.selectedSize != null)
                  Text(
                    '${item.selectedColor ?? ''} ${item.selectedSize ?? ''}'
                        .trim(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      AppConstants.formatPrice(item.product.price),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const Spacer(),
                    // أزرار الكمية
                    _buildQuantityControls(item, index),
                  ],
                ),
              ],
            ),
          ),

          // زر الحذف
          IconButton(
            onPressed: () => _removeItem(index),
            icon: const Icon(Icons.delete_outline, color: AppColors.errorColor),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControls(CartItem item, int index) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => _decreaseQuantity(index),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.remove,
              size: 16,
              color: AppColors.primaryText,
            ),
          ),
        ),
        Container(
          width: 40,
          alignment: Alignment.center,
          child: Text(
            item.quantity.toString(),
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
        GestureDetector(
          onTap: () => _increaseQuantity(index),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.add, size: 16, color: AppColors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderSummary() {
    final subtotal = _appState.cartSubtotal;
    final shipping = _appState.cartShipping;
    final total = _appState.cartTotal;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
          topRight: Radius.circular(AppDimensions.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الفرعي',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              Text(
                AppConstants.formatPrice(subtotal),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الشحن', style: Theme.of(context).textTheme.bodyLarge),
              Text(
                shipping == 0 ? 'مجاني' : AppConstants.formatPrice(shipping),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: shipping == 0 ? AppColors.success : null,
                ),
              ),
            ],
          ),
          if (shipping == 0)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'شحن مجاني للطلبات أكثر من ${AppConstants.formatPrice(200000)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.success),
              ),
            ),
          const Divider(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الكلي',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                AppConstants.formatPrice(total),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutBar() {
    final total = _appState.cartTotal;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _proceedToCheckout,
            child: Text(
              'متابعة الدفع (${AppConstants.formatPrice(total)})',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }

  void _increaseQuantity(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      _appState.updateCartItemQuantity(item.id, item.quantity + 1);
      _saveCartData();
    }
  }

  void _decreaseQuantity(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      if (item.quantity > 1) {
        _appState.updateCartItemQuantity(item.id, item.quantity - 1);
        _saveCartData();
      }
    }
  }

  void _removeItem(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      _appState.removeFromCart(item.id);
      _saveCartData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف المنتج من السلة'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _clearCart() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح السلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _appState.clearCart();
              _saveCartData();
              Navigator.of(context).pop();
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _proceedToCheckout() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CheckoutPage()),
    );
  }
}
