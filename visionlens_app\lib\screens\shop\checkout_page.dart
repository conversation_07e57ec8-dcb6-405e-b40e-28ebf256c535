import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';
import '../../models/order.dart';
import '../../models/user_simple.dart' as user_model;

class CheckoutPage extends StatefulWidget {
  const CheckoutPage({super.key});

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  final AppState _appState = AppState();
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _notesController = TextEditingController();
  
  PaymentMethod _selectedPaymentMethod = PaymentMethod.cash;
  ShippingMethod _selectedShippingMethod = ShippingMethod.standard;
  bool _isLoading = false;
  bool _saveAddress = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final user = _appState.currentUser;
    if (user != null) {
      _firstNameController.text = user.firstName;
      _lastNameController.text = user.lastName;
      _phoneController.text = user.phone ?? '';
      _addressController.text = user.address ?? '';
      _cityController.text = user.city ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إتمام الطلب'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
      ),
      body: ListenableBuilder(
        listenable: _appState,
        builder: (context, child) {
          final cartItems = _appState.cartItems;
          
          if (cartItems.isEmpty) {
            return _buildEmptyCart();
          }
          
          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildOrderSummary(),
                  _buildShippingInfo(),
                  _buildPaymentMethod(),
                  _buildOrderNotes(),
                  const SizedBox(height: 100), // مساحة للزر السفلي
                ],
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: _buildCheckoutButton(),
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: AppColors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'السلة فارغة',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          const Text('أضف منتجات إلى السلة لإتمام الطلب'),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('العودة للتسوق'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    final cartItems = _appState.cartItems;
    final subtotal = _appState.cartSubtotal;
    final shipping = _getShippingCost();
    final total = subtotal + shipping;

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الطلب',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          // عناصر الطلب
          ...cartItems.take(3).map((item) => _buildOrderItem(item)),
          
          if (cartItems.length > 3) ...[
            const SizedBox(height: 8),
            Text(
              'و ${cartItems.length - 3} منتجات أخرى',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
          ],
          
          const Divider(height: 24),
          
          // المجاميع
          _buildSummaryRow('المجموع الفرعي', AppConstants.formatPrice(subtotal)),
          const SizedBox(height: 8),
          _buildSummaryRow('الشحن', AppConstants.formatPrice(shipping)),
          const Divider(height: 16),
          _buildSummaryRow(
            'المجموع الكلي',
            AppConstants.formatPrice(total),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(CartItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.scaffoldBackground,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                item.product.images.isNotEmpty ? item.product.images.first : '',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(Icons.image_not_supported);
                },
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'الكمية: ${item.quantity}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          Text(
            AppConstants.formatPrice(item.product.price * item.quantity),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: isTotal ? AppColors.primaryColor : null,
          ),
        ),
      ],
    );
  }

  Widget _buildShippingInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات التوصيل',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _firstNameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم الأول',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الاسم الأول مطلوب';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _lastNameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم الأخير',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الاسم الأخير مطلوب';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'رقم الهاتف',
              border: OutlineInputBorder(),
              prefixText: '+964 ',
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'رقم الهاتف مطلوب';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _cityController,
            decoration: const InputDecoration(
              labelText: 'المدينة',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'المدينة مطلوبة';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _addressController,
            decoration: const InputDecoration(
              labelText: 'العنوان التفصيلي',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'العنوان مطلوب';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // طريقة الشحن
          Text(
            'طريقة الشحن',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          
          Column(
            children: ShippingMethod.values.map((method) {
              return RadioListTile<ShippingMethod>(
                contentPadding: EdgeInsets.zero,
                title: Text(_getShippingMethodText(method)),
                subtitle: Text(_getShippingMethodDescription(method)),
                value: method,
                groupValue: _selectedShippingMethod,
                onChanged: (value) {
                  setState(() {
                    _selectedShippingMethod = value!;
                  });
                },
              );
            }).toList(),
          ),
          
          CheckboxListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('حفظ هذا العنوان للطلبات القادمة'),
            value: _saveAddress,
            onChanged: (value) {
              setState(() {
                _saveAddress = value ?? false;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethod() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طريقة الدفع',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Column(
            children: PaymentMethod.values.map((method) {
              return RadioListTile<PaymentMethod>(
                contentPadding: EdgeInsets.zero,
                title: Row(
                  children: [
                    Icon(_getPaymentMethodIcon(method)),
                    const SizedBox(width: 8),
                    Text(_getPaymentMethodText(method)),
                  ],
                ),
                subtitle: Text(_getPaymentMethodDescription(method)),
                value: method,
                groupValue: _selectedPaymentMethod,
                onChanged: (value) {
                  setState(() {
                    _selectedPaymentMethod = value!;
                  });
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderNotes() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملاحظات الطلب (اختياري)',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              hintText: 'أضف أي ملاحظات خاصة بطلبك...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutButton() {
    final total = _appState.cartSubtotal + _getShippingCost();
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _placeOrder,
            child: _isLoading
                ? const CircularProgressIndicator(color: AppColors.white)
                : Text(
                    'تأكيد الطلب (${AppConstants.formatPrice(total)})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  double _getShippingCost() {
    switch (_selectedShippingMethod) {
      case ShippingMethod.standard:
        return _appState.cartSubtotal >= 200000 ? 0 : 15000;
      case ShippingMethod.express:
        return 25000;
      case ShippingMethod.overnight:
        return 40000;
      case ShippingMethod.pickup:
        return 0;
    }
  }

  String _getShippingMethodText(ShippingMethod method) {
    switch (method) {
      case ShippingMethod.standard:
        return 'شحن عادي (2-3 أيام)';
      case ShippingMethod.express:
        return 'شحن سريع (1-2 أيام)';
      case ShippingMethod.overnight:
        return 'شحن خلال الليل';
      case ShippingMethod.pickup:
        return 'استلام من المتجر';
    }
  }

  String _getShippingMethodDescription(ShippingMethod method) {
    switch (method) {
      case ShippingMethod.standard:
        return 'مجاني للطلبات أكثر من 200,000 د.ع';
      case ShippingMethod.express:
        return '25,000 د.ع';
      case ShippingMethod.overnight:
        return '40,000 د.ع';
      case ShippingMethod.pickup:
        return 'مجاني';
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'الدفع عند الاستلام';
      case PaymentMethod.card:
        return 'بطاقة ائتمان';
      case PaymentMethod.wallet:
        return 'محفظة إلكترونية';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
    }
  }

  String _getPaymentMethodDescription(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'ادفع نقداً عند وصول الطلب';
      case PaymentMethod.card:
        return 'Visa, MasterCard, أو بطاقات أخرى';
      case PaymentMethod.wallet:
        return 'ZainCash, AsiaHawala, أو محافظ أخرى';
      case PaymentMethod.bankTransfer:
        return 'تحويل مباشر إلى حساب المتجر';
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.wallet:
        return Icons.account_balance_wallet;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
    }
  }

  Future<void> _placeOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء الطلب
      final order = Order(
        id: 'ORD${DateTime.now().millisecondsSinceEpoch}',
        date: DateTime.now(),
        status: OrderStatus.processing,
        total: _appState.cartSubtotal + _getShippingCost(),
        items: _appState.cartItems.map((cartItem) {
          return OrderItem(
            productId: cartItem.product.id,
            productName: cartItem.product.name,
            imageUrl: cartItem.product.images.isNotEmpty 
                ? cartItem.product.images.first 
                : '',
            quantity: cartItem.quantity,
            price: cartItem.product.price,
            selectedColor: cartItem.selectedColor,
            selectedSize: cartItem.selectedSize,
          );
        }).toList(),
        notes: _notesController.text.isEmpty ? null : _notesController.text,
      );

      // محاكاة معالجة الطلب
      await Future.delayed(const Duration(seconds: 2));

      // مسح السلة
      _appState.clearCart();
      await StorageService.saveCart([]);

      if (mounted) {
        // عرض رسالة نجاح
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('تم تأكيد الطلب!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text('رقم الطلب: ${order.id}'),
                const SizedBox(height: 8),
                const Text('سيتم التواصل معك قريباً لتأكيد التفاصيل'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // إغلاق الحوار
                  Navigator.of(context).pop(); // العودة للصفحة السابقة
                },
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تأكيد الطلب: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
