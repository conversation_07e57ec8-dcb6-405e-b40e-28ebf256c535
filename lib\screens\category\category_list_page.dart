import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../app_properties.dart';
import '../../models/category.dart' as category_model;
import '../../models/product.dart';
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';

import 'category_products_page.dart';

class CategoryListPage extends StatefulWidget {
  const CategoryListPage({super.key});

  @override
  State<CategoryListPage> createState() => _CategoryListPageState();
}

class _CategoryListPageState extends State<CategoryListPage> {
  List<category_model.Category> _categories = [];
  Map<String, int> _categoryProductCounts = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      // جلب الفئات من Firestore أولاً
      List<category_model.Category> categories = [];
      List<Product> products = [];

      try {
        categories = await FirestoreDataService.getCategories();
        products = await FirestoreDataService.getProducts();
        if (kDebugMode) {
          print(
            '✅ تم جلب ${categories.length} فئة و ${products.length} منتج من Firestore',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل في جلب البيانات من Firestore: $e');
          print('🔄 التبديل للبيانات المحلية...');
        }
        // في حالة فشل Firestore، استخدم البيانات المحلية
        categories = await ApiService.getCategories();
        products = await ApiService.getProducts();
      }

      // حساب عدد المنتجات لكل فئة
      Map<String, int> productCounts = {};
      for (var category in categories) {
        final count = products.where((p) => p.categoryId == category.id).length;
        productCounts[category.id] = count;
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _categoryProductCounts = productCounts;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _categories = [];
          _categoryProductCounts = {};
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('الفئات'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadCategories,
              child: _categories.isEmpty
                  ? _buildEmptyState()
                  : _buildCategoriesGrid(),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.category_outlined,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فئات متاحة',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة الفئات قريباً',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.secondaryText),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      child: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: AppDimensions.marginMedium,
        crossAxisSpacing: AppDimensions.marginMedium,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return _buildCategoryCard(category, index);
        },
      ),
    );
  }

  Widget _buildCategoryCard(category_model.Category category, int index) {
    // تنويع ارتفاع البطاقات لتصميم أكثر جاذبية
    final bool isLarge = index % 3 == 0;
    final double height = isLarge ? 200 : 160;

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CategoryProductsPage(category: category),
          ),
        );
      },
      child: Container(
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.08),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
          child: Stack(
            children: [
              // خلفية متدرجة
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      category.primaryColor.withValues(alpha: 0.8),
                      category.secondaryColor.withValues(alpha: 0.6),
                    ],
                  ),
                ),
              ),

              // نمط خلفية
              Positioned(
                right: -20,
                bottom: -20,
                child: Icon(
                  Icons.visibility,
                  size: 80,
                  color: AppColors.white.withValues(alpha: 0.1),
                ),
              ),

              // محتوى البطاقة
              Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // أيقونة الفئة
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: AppColors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        _getCategoryIcon(category.id),
                        color: AppColors.white,
                        size: 28,
                      ),
                    ),

                    const Spacer(),

                    // اسم الفئة
                    Text(
                      category.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // وصف الفئة
                    Text(
                      category.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.white.withValues(alpha: 0.9),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // عدد المنتجات
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_categoryProductCounts[category.id] ?? 0} منتج',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // مؤشر الفئات الفرعية
              if (category.hasSubcategories)
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppColors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.white,
                      size: 16,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'eyeglasses':
        return Icons.visibility;
      case 'sunglasses':
        return Icons.wb_sunny;
      case 'contact_lenses':
        return Icons.remove_red_eye;
      case 'reading_glasses':
        return Icons.menu_book;
      case 'accessories':
        return Icons.shopping_bag;
      case 'solutions':
        return Icons.local_pharmacy;
      default:
        return Icons.category;
    }
  }
}
