import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/data_service.dart';
import '../../services/firestore_data_service.dart';

class OrderTrackingPage extends StatefulWidget {
  const OrderTrackingPage({super.key});

  @override
  State<OrderTrackingPage> createState() => _OrderTrackingPageState();
}

class _OrderTrackingPageState extends State<OrderTrackingPage>
    with WidgetsBindingObserver {
  final AppState _appState = AppState();
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadOrders();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // إعادة تحميل البيانات عند العودة للتطبيق
      _loadOrders();
    }
  }

  Future<void> _loadOrders() async {
    try {
      // جلب الطلبات من Firestore أولاً
      List<Map<String, dynamic>> ordersData = [];
      try {
        ordersData = await FirestoreDataService.getOrders();
        if (kDebugMode) {
          print('✅ تم جلب ${ordersData.length} طلب من Firestore');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل في جلب الطلبات من Firestore: $e');
          print('🔄 التبديل للبيانات المحلية...');
        }
        // في حالة فشل Firestore، استخدم البيانات المحلية
        ordersData = await DataService.getOrders();
      }

      // فلترة الطلبات للمستخدم الحالي
      final currentUser = _appState.currentUser;
      if (currentUser != null) {
        ordersData = ordersData
            .where(
              (order) =>
                  order['userId'] == currentUser.id ||
                  order['customerEmail'] == currentUser.email,
            )
            .toList();

        if (kDebugMode) {
          print(
            '🔍 تم فلترة ${ordersData.length} طلب للمستخدم ${currentUser.email}',
          );
          for (var order in ordersData) {
            print(
              '📦 طلب: ${order['id']} - الحالة: ${order['status']} - التاريخ: ${order['createdAt']}',
            );
          }
        }
      }

      setState(() {
        _orders = ordersData;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الطلبات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تتبع الشحن'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _orders.isEmpty
          ? _buildEmptyState()
          : RefreshIndicator(onRefresh: _loadOrders, child: _buildOrdersList()),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_shipping_outlined,
            size: 80,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلبات للتتبع',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 8),
          Text(
            'عندما تقوم بطلب منتجات، ستظهر هنا لتتبع حالة الشحن',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.secondaryText),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _orders.length,
      itemBuilder: (context, index) {
        final order = _orders[index];
        return _buildOrderCard(order);
      },
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    final status = order['status'] ?? 'pending';
    final orderId = order['id'] ?? '';
    // final customerName = order['customerName'] ?? 'غير محدد'; // غير مستخدم
    final total = order['total'] ?? 0.0;
    // final currency = order['currency'] ?? 'IQD'; // غير مستخدم
    final createdAt = order['createdAt'] ?? '';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طلب #${orderId.substring(0, 8)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusChip(status),
              ],
            ),
            const SizedBox(height: 12),
            _buildTrackingTimeline(status),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المجموع: ${AppConstants.formatPrice(total.toDouble())}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _formatDate(createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;
    String statusText;

    switch (status.toLowerCase()) {
      case 'pending':
        backgroundColor = AppColors.warningColor;
        textColor = AppColors.white;
        statusText = 'قيد المراجعة';
        break;
      case 'confirmed':
        backgroundColor = AppColors.primaryColor;
        textColor = AppColors.white;
        statusText = 'مؤكد';
        break;
      case 'processing':
        backgroundColor = AppColors.primaryColor;
        textColor = AppColors.white;
        statusText = 'قيد التحضير';
        break;
      case 'shipped':
        backgroundColor = AppColors.frameGold;
        textColor = AppColors.white;
        statusText = 'تم الشحن';
        break;
      case 'delivered':
        backgroundColor = AppColors.success;
        textColor = AppColors.white;
        statusText = 'تم التسليم';
        break;
      case 'cancelled':
        backgroundColor = AppColors.errorColor;
        textColor = AppColors.white;
        statusText = 'ملغي';
        break;
      default:
        backgroundColor = AppColors.grey;
        textColor = AppColors.white;
        statusText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTrackingTimeline(String currentStatus) {
    final steps = [
      {'status': 'pending', 'title': 'تم استلام الطلب', 'icon': Icons.receipt},
      {
        'status': 'confirmed',
        'title': 'تم تأكيد الطلب',
        'icon': Icons.check_circle,
      },
      {'status': 'processing', 'title': 'قيد التحضير', 'icon': Icons.inventory},
      {'status': 'shipped', 'title': 'تم الشحن', 'icon': Icons.local_shipping},
      {'status': 'delivered', 'title': 'تم التسليم', 'icon': Icons.home},
    ];

    final currentIndex = steps.indexWhere(
      (step) => step['status'] == currentStatus,
    );

    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isCompleted = index <= currentIndex;
        final isCurrent = index == currentIndex;

        return Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isCompleted ? AppColors.success : AppColors.lightGrey,
                shape: BoxShape.circle,
                border: isCurrent
                    ? Border.all(color: AppColors.primaryColor, width: 3)
                    : null,
              ),
              child: Icon(
                step['icon'] as IconData,
                color: isCompleted ? AppColors.white : AppColors.grey,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                step['title'] as String,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isCompleted
                      ? AppColors.primaryText
                      : AppColors.secondaryText,
                  fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
