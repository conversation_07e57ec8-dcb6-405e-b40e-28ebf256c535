import 'package:flutter/material.dart';

// ألوان التطبيق الرئيسية
class AppColors {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2196F3); // أزرق
  static const Color secondaryColor = Color(0xFFFFC107); // أصفر ذهبي
  static const Color accentColor = Color(0xFF4CAF50); // أخضر
  static const Color backgroundColor = Color(0xFFF5F5F5); // رمادي فاتح
  static const Color textColor = Color(0xFF212121); // رمادي داكن
  static const Color errorColor = Color(0xFFF44336); // أحمر

  // ألوان خاصة بمتجر النظارات
  static const Color lensBlue = Color(0xFF1976D2); // أزرق العدسات
  static const Color frameGold = Color(0xFFFFB300); // ذهبي الإطارات
  static const Color visionGreen = Color(0xFF388E3C); // أخضر الرؤية

  // ألوان إضافية
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color lightGrey = Color(0xFFE0E0E0);
  static const Color darkGrey = Color(0xFF424242);

  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);
  static const Color danger = Color(0xFFF44336);

  // ألوان الخلفية
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color scaffoldBackground = Color(0xFFF5F5F5);
  static const Color appBarBackground = Color(0xFF2196F3);

  // ألوان النص
  static const Color primaryText = Color(0xFF212121);
  static const Color secondaryText = Color(0xFF757575);
  static const Color hintText = Color(0xFF9E9E9E);
  static const Color disabledText = Color(0xFFBDBDBD);
}

// ثيم التطبيق
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: AppColors.primaryColor,
      scaffoldBackgroundColor: AppColors.scaffoldBackground,
      // fontFamily: 'Cairo',

      // شريط التطبيق
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.appBarBackground,
        foregroundColor: AppColors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.white,
        ),
      ),

      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // البطاقات
      cardTheme: CardThemeData(
        color: AppColors.cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(8),
      ),

      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.lightGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.lightGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.errorColor),
        ),
        labelStyle: const TextStyle(
          color: AppColors.secondaryText,
          fontFamily: 'Cairo',
        ),
        hintStyle: const TextStyle(
          color: AppColors.hintText,
          fontFamily: 'Cairo',
        ),
      ),

      // النصوص
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.normal,
          color: AppColors.secondaryText,
          fontFamily: 'Cairo',
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.primaryText,
          fontFamily: 'Cairo',
        ),
        labelMedium: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: AppColors.secondaryText,
          fontFamily: 'Cairo',
        ),
        labelSmall: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: AppColors.secondaryText,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }
}

// أبعاد التطبيق
class AppDimensions {
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginXLarge = 32.0;

  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;

  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;

  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
}

// ثوابت التطبيق
class AppConstants {
  static const String appName = 'VisionLens';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'متجر إلكتروني للنظارات والعدسات اللاصقة';

  // URLs
  static const String baseUrl = 'https://api.visionlens.com';
  static const String termsUrl = 'https://visionlens.com/terms';
  static const String privacyUrl = 'https://visionlens.com/privacy';
  static const String supportUrl = 'https://visionlens.com/support';

  // مفاتيح التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String cartDataKey = 'cart_data';
  static const String favoritesKey = 'favorites';
  static const String settingsKey = 'settings';

  // أرقام الهواتف
  static const String supportPhone = '+964123456789';
  static const String emergencyPhone = '+964987654321';

  // البريد الإلكتروني
  static const String supportEmail = '<EMAIL>';
  static const String infoEmail = '<EMAIL>';

  // العملة
  static const String currency = 'د.ع';
  static const String currencyCode = 'IQD';
  static const String currencyName = 'دينار عراقي';

  // تنسيق العملة
  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} $currency';
  }

  static String formatPrice(double price) {
    // تنسيق الأسعار بالدينار العراقي
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}م $currency';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}ألف $currency';
    } else {
      return '${price.toStringAsFixed(0)} $currency';
    }
  }
}
