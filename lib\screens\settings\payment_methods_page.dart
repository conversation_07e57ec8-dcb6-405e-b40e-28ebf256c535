import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../models/payment_method.dart';
import '../../services/storage_service.dart';

class PaymentMethodsPage extends StatefulWidget {
  const PaymentMethodsPage({super.key});

  @override
  State<PaymentMethodsPage> createState() => _PaymentMethodsPageState();
}

class _PaymentMethodsPageState extends State<PaymentMethodsPage> {
  List<PaymentMethod> _paymentMethods = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final methods = await StorageService.getPaymentMethods();
      if (mounted) {
        setState(() {
          _paymentMethods = methods;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _paymentMethods = [];
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طرق الدفع'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // طرق الدفع المتاحة
                _buildAvailablePaymentMethods(),
                
                // طرق الدفع المحفوظة
                if (_paymentMethods.isNotEmpty) ...[
                  const SizedBox(height: 20),
                  _buildSavedPaymentMethods(),
                ],
              ],
            ),
    );
  }

  Widget _buildAvailablePaymentMethods() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طرق الدفع المتاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // الدفع عند الاستلام
          _buildPaymentMethodCard(
            icon: Icons.money,
            title: 'الدفع عند الاستلام',
            subtitle: 'ادفع نقداً عند وصول الطلب',
            isRecommended: true,
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('الدفع عند الاستلام متاح في جميع الطلبات'),
                  backgroundColor: AppColors.successColor,
                ),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // البطاقات البنكية
          _buildPaymentMethodCard(
            icon: Icons.credit_card,
            title: 'البطاقات البنكية',
            subtitle: 'فيزا، ماستركارد، وبطاقات محلية',
            isComingSoon: true,
            onTap: () => _showComingSoon('البطاقات البنكية'),
          ),
          
          const SizedBox(height: 12),
          
          // المحافظ الإلكترونية
          _buildPaymentMethodCard(
            icon: Icons.account_balance_wallet,
            title: 'المحافظ الإلكترونية',
            subtitle: 'زين كاش، آسيا حوالة، فاست باي',
            isComingSoon: true,
            onTap: () => _showComingSoon('المحافظ الإلكترونية'),
          ),
          
          const SizedBox(height: 12),
          
          // التحويل البنكي
          _buildPaymentMethodCard(
            icon: Icons.account_balance,
            title: 'التحويل البنكي',
            subtitle: 'تحويل مباشر للحساب البنكي',
            isComingSoon: true,
            onTap: () => _showComingSoon('التحويل البنكي'),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodCard({
    required IconData icon,
    required String title,
    required String subtitle,
    bool isRecommended = false,
    bool isComingSoon = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isRecommended 
                ? AppColors.primaryColor 
                : AppColors.lightGrey,
            width: isRecommended ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          color: isRecommended 
              ? AppColors.primaryColor.withValues(alpha: 0.05)
              : AppColors.white,
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isRecommended 
                    ? AppColors.primaryColor.withValues(alpha: 0.1)
                    : AppColors.lightGrey,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: isRecommended 
                    ? AppColors.primaryColor 
                    : AppColors.grey,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isRecommended 
                              ? AppColors.primaryColor 
                              : AppColors.primaryText,
                        ),
                      ),
                      if (isRecommended) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'موصى به',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                      if (isComingSoon) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.frameGold,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'قريباً',
                            style: TextStyle(
                              color: AppColors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: isRecommended 
                  ? AppColors.primaryColor 
                  : AppColors.grey,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSavedPaymentMethods() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طرق الدفع المحفوظة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton.icon(
                onPressed: () => _showComingSoon('إضافة طريقة دفع'),
                icon: const Icon(Icons.add, size: 16),
                label: const Text('إضافة'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // قائمة طرق الدفع المحفوظة
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _paymentMethods.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final method = _paymentMethods[index];
              return _buildSavedPaymentMethodCard(method);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSavedPaymentMethodCard(PaymentMethod method) {
    IconData icon;
    Color color;
    
    switch (method.type) {
      case PaymentMethodType.creditCard:
        icon = Icons.credit_card;
        color = AppColors.primaryColor;
        break;
      case PaymentMethodType.debitCard:
        icon = Icons.payment;
        color = AppColors.lensBlue;
        break;
      case PaymentMethodType.wallet:
        icon = Icons.account_balance_wallet;
        color = AppColors.frameGold;
        break;
      case PaymentMethodType.bankTransfer:
        icon = Icons.account_balance;
        color = AppColors.success;
        break;
      default:
        icon = Icons.payment;
        color = AppColors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightGrey),
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          
          const SizedBox(width: 16),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  method.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  method.details,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          
          if (method.isDefault) ...[
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'افتراضي',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          PopupMenuButton<String>(
            onSelected: (value) => _handlePaymentMethodAction(value, method),
            itemBuilder: (context) => [
              if (!method.isDefault)
                const PopupMenuItem(
                  value: 'set_default',
                  child: Text('تعيين كافتراضي'),
                ),
              const PopupMenuItem(
                value: 'edit',
                child: Text('تعديل'),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Text('حذف'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handlePaymentMethodAction(String action, PaymentMethod method) {
    switch (action) {
      case 'set_default':
        _setDefaultPaymentMethod(method);
        break;
      case 'edit':
        _editPaymentMethod(method);
        break;
      case 'delete':
        _deletePaymentMethod(method);
        break;
    }
  }

  void _setDefaultPaymentMethod(PaymentMethod method) async {
    try {
      await StorageService.setDefaultPaymentMethod(method.id);
      _loadPaymentMethods();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعيين طريقة الدفع كافتراضية'),
            backgroundColor: AppColors.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }

  void _editPaymentMethod(PaymentMethod method) {
    _showComingSoon('تعديل طريقة الدفع');
  }

  void _deletePaymentMethod(PaymentMethod method) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف طريقة الدفع'),
        content: Text('هل أنت متأكد من حذف ${method.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              try {
                await StorageService.deletePaymentMethod(method.id);
                _loadPaymentMethods();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف طريقة الدفع'),
                      backgroundColor: AppColors.successColor,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: $e'),
                      backgroundColor: AppColors.errorColor,
                    ),
                  );
                }
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature - سيتم تنفيذ هذه الميزة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
