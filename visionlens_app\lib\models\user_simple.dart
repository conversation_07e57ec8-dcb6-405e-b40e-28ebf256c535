class User {
  final String id;
  final String email;
  final String? phone;
  final String firstName;
  final String lastName;
  final String? profileImage;
  final DateTime? dateOfBirth;
  final Gender? gender;
  final String? country;
  final String? city;
  final String? address;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final UserStatus status;

  const User({
    required this.id,
    required this.email,
    this.phone,
    required this.firstName,
    required this.lastName,
    this.profileImage,
    this.dateOfBirth,
    this.gender,
    this.country,
    this.city,
    this.address,
    required this.createdAt,
    required this.updatedAt,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
    this.status = UserStatus.active,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      profileImage: json['profileImage'],
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'])
          : null,
      gender: json['gender'] != null
          ? Gender.values.firstWhere(
              (e) => e.toString().split('.').last == json['gender'],
              orElse: () => Gender.other,
            )
          : null,
      country: json['country'],
      city: json['city'],
      address: json['address'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
      status: UserStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => UserStatus.active,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'firstName': firstName,
      'lastName': lastName,
      'profileImage': profileImage,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender?.toString().split('.').last,
      'country': country,
      'city': city,
      'address': address,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'status': status.toString().split('.').last,
    };
  }

  // الاسم الكامل
  String get fullName => '$firstName $lastName';

  // التحقق من اكتمال الملف الشخصي
  bool get isProfileComplete {
    return email.isNotEmpty &&
        firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        phone != null &&
        phone!.isNotEmpty;
  }

  // التحقق من التحقق الكامل
  bool get isFullyVerified {
    return isEmailVerified && isPhoneVerified;
  }

  // نسخة محدثة من المستخدم
  User copyWith({
    String? id,
    String? email,
    String? phone,
    String? firstName,
    String? lastName,
    String? profileImage,
    DateTime? dateOfBirth,
    Gender? gender,
    String? country,
    String? city,
    String? address,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    UserStatus? status,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileImage: profileImage ?? this.profileImage,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      country: country ?? this.country,
      city: city ?? this.city,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      status: status ?? this.status,
    );
  }
}

// حالة المستخدم
enum UserStatus { active, inactive, suspended, pending }

// الجنس
enum Gender { male, female, other }

// العنوان
class Address {
  final String id;
  final String title;
  final String firstName;
  final String lastName;
  final String phone;
  final String country;
  final String city;
  final String district;
  final String street;
  final String buildingNumber;
  final String? apartmentNumber;
  final String? postalCode;
  final String? additionalInfo;
  final bool isDefault;
  final AddressType type;
  final double? latitude;
  final double? longitude;

  const Address({
    required this.id,
    required this.title,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.country,
    required this.city,
    required this.district,
    required this.street,
    required this.buildingNumber,
    this.apartmentNumber,
    this.postalCode,
    this.additionalInfo,
    this.isDefault = false,
    this.type = AddressType.home,
    this.latitude,
    this.longitude,
  });

  // العنوان الكامل
  String get fullAddress {
    String address = '$street, $district, $city, $country';
    if (buildingNumber.isNotEmpty) {
      address = 'مبنى $buildingNumber, $address';
    }
    if (apartmentNumber != null && apartmentNumber!.isNotEmpty) {
      address = 'شقة $apartmentNumber, $address';
    }
    return address;
  }

  // وصف نوع العنوان
  String get typeDescription {
    switch (type) {
      case AddressType.home:
        return 'المنزل';
      case AddressType.work:
        return 'العمل';
      case AddressType.other:
        return 'أخرى';
    }
  }

  // نسخة محدثة من العنوان
  Address copyWith({
    String? id,
    String? title,
    String? firstName,
    String? lastName,
    String? phone,
    String? country,
    String? city,
    String? district,
    String? street,
    String? buildingNumber,
    String? apartmentNumber,
    String? postalCode,
    String? additionalInfo,
    bool? isDefault,
    AddressType? type,
    double? latitude,
    double? longitude,
  }) {
    return Address(
      id: id ?? this.id,
      title: title ?? this.title,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      country: country ?? this.country,
      city: city ?? this.city,
      district: district ?? this.district,
      street: street ?? this.street,
      buildingNumber: buildingNumber ?? this.buildingNumber,
      apartmentNumber: apartmentNumber ?? this.apartmentNumber,
      postalCode: postalCode ?? this.postalCode,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      isDefault: isDefault ?? this.isDefault,
      type: type ?? this.type,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}

// نوع العنوان
enum AddressType { home, work, other }
