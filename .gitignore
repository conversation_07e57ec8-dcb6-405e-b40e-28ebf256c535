# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Firebase
/android/app/google-services.json
/ios/Runner/GoogleService-Info.plist
/web/firebase-config.js

# Environment variables
.env
.env.local
.env.production

# Keys and certificates
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.cer
android/app/key.properties

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.js
*.bat

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Build outputs
/build/
/dist/
/out/

# Local configuration
android/local.properties
ios/Flutter/flutter_export_environment.sh
