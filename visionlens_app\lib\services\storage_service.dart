import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_state.dart';
import '../models/user_simple.dart' as user_model;
import '../models/product.dart';
import '../models/category.dart';
import '../models/order.dart';

class StorageService {
  static const String _cartKey = 'cart_items';
  static const String _wishlistKey = 'wishlist_items';
  static const String _userKey = 'current_user';
  static const String _searchHistoryKey = 'search_history';

  // حفظ السلة
  static Future<void> saveCart(List<CartItem> cartItems) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartJson = cartItems.map((item) => item.toJson()).toList();
      await prefs.setString(_cartKey, jsonEncode(cartJson));
    } catch (e) {
      print('خطأ في حفظ السلة: $e');
    }
  }

  // تحميل السلة
  static Future<List<CartItem>> loadCart() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartString = prefs.getString(_cartKey);
      if (cartString != null) {
        final cartJson = jsonDecode(cartString) as List;
        return cartJson.map((item) => CartItem.fromJson(item)).toList();
      }
    } catch (e) {
      print('خطأ في تحميل السلة: $e');
    }
    return [];
  }

  // حفظ المفضلة
  static Future<void> saveWishlist(List<WishlistItem> wishlistItems) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistJson = wishlistItems.map((item) => item.toJson()).toList();
      await prefs.setString(_wishlistKey, jsonEncode(wishlistJson));
    } catch (e) {
      print('خطأ في حفظ المفضلة: $e');
    }
  }

  // تحميل المفضلة
  static Future<List<WishlistItem>> loadWishlist() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistString = prefs.getString(_wishlistKey);
      if (wishlistString != null) {
        final wishlistJson = jsonDecode(wishlistString) as List;
        return wishlistJson.map((item) => WishlistItem.fromJson(item)).toList();
      }
    } catch (e) {
      print('خطأ في تحميل المفضلة: $e');
    }
    return [];
  }

  // حفظ المستخدم
  static Future<void> saveUser(user_model.User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, jsonEncode(user.toJson()));
    } catch (e) {
      print('خطأ في حفظ المستخدم: $e');
    }
  }

  // تحميل المستخدم
  static Future<user_model.User?> loadUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userString = prefs.getString(_userKey);
      if (userString != null) {
        final userJson = jsonDecode(userString);
        return user_model.User.fromJson(userJson);
      }
    } catch (e) {
      print('خطأ في تحميل المستخدم: $e');
    }
    return null;
  }

  // مسح بيانات المستخدم
  static Future<void> clearUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
    } catch (e) {
      print('خطأ في مسح بيانات المستخدم: $e');
    }
  }

  // تاريخ البحث

  static Future<void> saveSearchHistory(List<String> history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_searchHistoryKey, history);
    } catch (e) {
      print('خطأ في حفظ تاريخ البحث: $e');
    }
  }

  static Future<List<String>> loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_searchHistoryKey) ?? [];
    } catch (e) {
      print('خطأ في تحميل تاريخ البحث: $e');
      return [];
    }
  }

  static Future<void> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_searchHistoryKey);
    } catch (e) {
      print('خطأ في مسح تاريخ البحث: $e');
    }
  }

  // إعدادات الإشعارات
  static const String _notificationSettingsKey = 'notification_settings';

  static Future<void> saveNotificationSettings(
    Map<String, dynamic> settings,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_notificationSettingsKey, jsonEncode(settings));
    } catch (e) {
      print('خطأ في حفظ إعدادات الإشعارات: $e');
    }
  }

  static Future<Map<String, dynamic>> loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString(_notificationSettingsKey);
      if (settingsString != null) {
        return jsonDecode(settingsString);
      }
    } catch (e) {
      print('خطأ في تحميل إعدادات الإشعارات: $e');
    }
    return {};
  }

  // إعدادات التطبيق العامة
  static const String _appSettingsKey = 'app_settings';

  static Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_appSettingsKey, jsonEncode(settings));
    } catch (e) {
      print('خطأ في حفظ إعدادات التطبيق: $e');
    }
  }

  static Future<Map<String, dynamic>> loadAppSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsString = prefs.getString(_appSettingsKey);
      if (settingsString != null) {
        return jsonDecode(settingsString);
      }
    } catch (e) {
      print('خطأ في تحميل إعدادات التطبيق: $e');
    }
    return {};
  }

  // مسح جميع البيانات
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      print('خطأ في مسح جميع البيانات: $e');
    }
  }

  // الحصول على حجم البيانات المخزنة
  static Future<int> getStorageSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      int totalSize = 0;

      for (final key in keys) {
        final value = prefs.get(key);
        if (value is String) {
          totalSize += value.length;
        }
      }

      return totalSize;
    } catch (e) {
      print('خطأ في حساب حجم التخزين: $e');
      return 0;
    }
  }

  // إضافة عنصر لتاريخ البحث
  static Future<void> addToSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    try {
      final history = await loadSearchHistory();

      // إزالة العنصر إذا كان موجود
      history.remove(query);

      // إضافة العنصر في المقدمة
      history.insert(0, query);

      // الاحتفاظ بآخر 10 عناصر فقط
      if (history.length > 10) {
        history.removeRange(10, history.length);
      }

      await saveSearchHistory(history);
    } catch (e) {
      print('خطأ في إضافة عنصر لتاريخ البحث: $e');
    }
  }

  // المنتجات
  static const String _productsKey = 'products';

  static Future<void> saveProducts(List<Product> products) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = products.map((product) => product.toJson()).toList();
      await prefs.setString(_productsKey, jsonEncode(productsJson));
    } catch (e) {
      print('خطأ في حفظ المنتجات: $e');
    }
  }

  static Future<List<Product>> loadProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsString = prefs.getString(_productsKey);
      if (productsString != null) {
        final productsJson = jsonDecode(productsString) as List;
        return productsJson
            .map((product) => Product.fromJson(product))
            .toList();
      }
    } catch (e) {
      print('خطأ في تحميل المنتجات: $e');
    }
    return [];
  }

  // الفئات
  static const String _categoriesKey = 'categories';

  static Future<void> saveCategories(List<Category> categories) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = categories
          .map((category) => category.toJson())
          .toList();
      await prefs.setString(_categoriesKey, jsonEncode(categoriesJson));
    } catch (e) {
      print('خطأ في حفظ الفئات: $e');
    }
  }

  static Future<List<Category>> loadCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesString = prefs.getString(_categoriesKey);
      if (categoriesString != null) {
        final categoriesJson = jsonDecode(categoriesString) as List;
        return categoriesJson
            .map((category) => Category.fromJson(category))
            .toList();
      }
    } catch (e) {
      print('خطأ في تحميل الفئات: $e');
    }
    return [];
  }

  // الطلبات
  static const String _ordersKey = 'orders';

  static Future<void> saveOrders(List<Order> orders) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = orders.map((order) => order.toJson()).toList();
      await prefs.setString(_ordersKey, jsonEncode(ordersJson));
    } catch (e) {
      print('خطأ في حفظ الطلبات: $e');
    }
  }

  static Future<void> saveOrder(Order order) async {
    try {
      final orders = await loadOrders();
      final existingIndex = orders.indexWhere((o) => o.id == order.id);

      if (existingIndex != -1) {
        orders[existingIndex] = order;
      } else {
        orders.insert(0, order);
      }

      await saveOrders(orders);
    } catch (e) {
      print('خطأ في حفظ الطلب: $e');
    }
  }

  static Future<List<Order>> loadOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersString = prefs.getString(_ordersKey);
      if (ordersString != null) {
        final ordersJson = jsonDecode(ordersString) as List;
        return ordersJson.map((order) => Order.fromJson(order)).toList();
      }
    } catch (e) {
      print('خطأ في تحميل الطلبات: $e');
    }
    return [];
  }
}
