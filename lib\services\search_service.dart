import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/product.dart';
// import '../models/category.dart'; // غير مستخدم حالياً
import 'storage_service.dart';

class SearchService extends ChangeNotifier {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  // قائمة المنتجات للبحث فيها
  List<Product> _allProducts = [];

  // نتائج البحث الحالية
  List<Product> _searchResults = [];
  List<Product> get searchResults => List.unmodifiable(_searchResults);

  // تاريخ البحث
  List<String> _searchHistory = [];
  List<String> get searchHistory => List.unmodifiable(_searchHistory);

  // الاقتراحات
  List<String> _suggestions = [];
  List<String> get suggestions => List.unmodifiable(_suggestions);

  // حالة التحميل
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // النص المبحوث عنه حالياً
  String _currentQuery = '';
  String get currentQuery => _currentQuery;

  // الفلاتر المطبقة
  SearchFilters _filters = const SearchFilters();
  SearchFilters get filters => _filters;

  // تهيئة الخدمة
  Future<void> initialize(List<Product> products) async {
    _allProducts = products;
    await _loadSearchHistory();
    _generateSuggestions();
    notifyListeners();
  }

  // البحث الأساسي
  Future<List<Product>> search(String query, {SearchFilters? filters}) async {
    if (query.trim().isEmpty) {
      _searchResults = [];
      _currentQuery = '';
      notifyListeners();
      return [];
    }

    _isLoading = true;
    _currentQuery = query.trim();
    _filters = filters ?? _filters;
    notifyListeners();

    // إضافة إلى تاريخ البحث
    await _addToSearchHistory(query);

    // تنفيذ البحث
    await Future.delayed(const Duration(milliseconds: 300)); // محاكاة التأخير

    _searchResults = _performSearch(query, _filters);
    _isLoading = false;
    notifyListeners();

    return _searchResults;
  }

  // البحث الفوري (أثناء الكتابة)
  Future<List<Product>> instantSearch(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    return _performSearch(query.trim(), _filters);
  }

  // تنفيذ البحث الفعلي
  List<Product> _performSearch(String query, SearchFilters filters) {
    final lowercaseQuery = query.toLowerCase();

    List<Product> results = _allProducts.where((product) {
      // البحث في الاسم
      bool matchesName = product.name.toLowerCase().contains(lowercaseQuery);

      // البحث في الوصف
      bool matchesDescription = product.description.toLowerCase().contains(
        lowercaseQuery,
      );

      // البحث في العلامة التجارية
      bool matchesBrand =
          product.brand?.toLowerCase().contains(lowercaseQuery) ?? false;

      // البحث في الفئة
      bool matchesCategory = product.categoryId.toLowerCase().contains(
        lowercaseQuery,
      );

      // البحث في الكلمات المفتاحية
      bool matchesKeywords =
          product.keywords?.any(
            (keyword) => keyword.toLowerCase().contains(lowercaseQuery),
          ) ??
          false;

      return matchesName ||
          matchesDescription ||
          matchesBrand ||
          matchesCategory ||
          matchesKeywords;
    }).toList();

    // تطبيق الفلاتر
    results = _applyFilters(results, filters);

    // ترتيب النتائج حسب الصلة
    results = _sortByRelevance(results, lowercaseQuery);

    return results;
  }

  // تطبيق الفلاتر
  List<Product> _applyFilters(List<Product> products, SearchFilters filters) {
    List<Product> filtered = products;

    // فلتر الفئة
    if (filters.categoryId != null) {
      filtered = filtered
          .where((p) => p.categoryId == filters.categoryId)
          .toList();
    }

    // فلتر العلامة التجارية
    if (filters.brand != null) {
      filtered = filtered.where((p) => p.brand == filters.brand).toList();
    }

    // فلتر السعر
    if (filters.minPrice != null) {
      filtered = filtered.where((p) => p.price >= filters.minPrice!).toList();
    }
    if (filters.maxPrice != null) {
      filtered = filtered.where((p) => p.price <= filters.maxPrice!).toList();
    }

    // فلتر التقييم
    if (filters.minRating != null) {
      filtered = filtered.where((p) => p.rating >= filters.minRating!).toList();
    }

    // فلتر التوفر
    if (filters.inStockOnly) {
      filtered = filtered.where((p) => p.stock > 0).toList();
    }

    // فلتر العروض
    if (filters.onSaleOnly) {
      filtered = filtered
          .where((p) => p.originalPrice != null && p.originalPrice! > p.price)
          .toList();
    }

    return filtered;
  }

  // ترتيب النتائج حسب الصلة
  List<Product> _sortByRelevance(List<Product> products, String query) {
    products.sort((a, b) {
      int scoreA = _calculateRelevanceScore(a, query);
      int scoreB = _calculateRelevanceScore(b, query);
      return scoreB.compareTo(scoreA); // ترتيب تنازلي
    });
    return products;
  }

  // حساب نقاط الصلة
  int _calculateRelevanceScore(Product product, String query) {
    int score = 0;
    final lowercaseQuery = query.toLowerCase();

    // نقاط إضافية للتطابق في الاسم
    if (product.name.toLowerCase().contains(lowercaseQuery)) {
      score += 100;
      if (product.name.toLowerCase().startsWith(lowercaseQuery)) {
        score += 50; // نقاط إضافية للبداية
      }
    }

    // نقاط للتطابق في العلامة التجارية
    if (product.brand?.toLowerCase().contains(lowercaseQuery) ?? false) {
      score += 75;
    }

    // نقاط للتطابق في الوصف
    if (product.description.toLowerCase().contains(lowercaseQuery)) {
      score += 25;
    }

    // نقاط للتطابق في الكلمات المفتاحية
    if (product.keywords?.any(
          (k) => k.toLowerCase().contains(lowercaseQuery),
        ) ??
        false) {
      score += 50;
    }

    // نقاط إضافية للمنتجات الشائعة
    score += (product.rating * 10).round();

    // نقاط إضافية للمنتجات المتوفرة
    if (product.stock > 0) {
      score += 20;
    }

    return score;
  }

  // الحصول على اقتراحات البحث
  List<String> getSearchSuggestions(String query) {
    if (query.trim().isEmpty) {
      return _suggestions.take(10).toList();
    }

    final lowercaseQuery = query.toLowerCase();
    List<String> suggestions = [];

    // اقتراحات من أسماء المنتجات
    for (final product in _allProducts) {
      if (product.name.toLowerCase().contains(lowercaseQuery)) {
        suggestions.add(product.name);
      }
      if (product.brand?.toLowerCase().contains(lowercaseQuery) ?? false) {
        suggestions.add(product.brand!);
      }
    }

    // اقتراحات من تاريخ البحث
    for (final historyItem in _searchHistory) {
      if (historyItem.toLowerCase().contains(lowercaseQuery)) {
        suggestions.add(historyItem);
      }
    }

    // إزالة التكرارات وترتيب
    suggestions = suggestions.toSet().toList();
    suggestions.sort((a, b) => a.length.compareTo(b.length));

    return suggestions.take(10).toList();
  }

  // تطبيق الفلاتر
  void applyFilters(SearchFilters filters) {
    _filters = filters;
    if (_currentQuery.isNotEmpty) {
      search(_currentQuery, filters: filters);
    }
  }

  // مسح الفلاتر
  void clearFilters() {
    _filters = const SearchFilters();
    if (_currentQuery.isNotEmpty) {
      search(_currentQuery);
    }
  }

  // إضافة إلى تاريخ البحث
  Future<void> _addToSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    _searchHistory.remove(query); // إزالة إذا كان موجود
    _searchHistory.insert(0, query); // إضافة في المقدمة

    // الاحتفاظ بآخر 20 عنصر فقط
    if (_searchHistory.length > 20) {
      _searchHistory = _searchHistory.take(20).toList();
    }

    await StorageService.saveSearchHistory(_searchHistory);
  }

  // تحميل تاريخ البحث
  Future<void> _loadSearchHistory() async {
    _searchHistory = await StorageService.loadSearchHistory();
  }

  // مسح تاريخ البحث
  Future<void> clearSearchHistory() async {
    _searchHistory.clear();
    await StorageService.clearSearchHistory();
    notifyListeners();
  }

  // حذف عنصر من تاريخ البحث
  Future<void> removeFromSearchHistory(String query) async {
    _searchHistory.remove(query);
    await StorageService.saveSearchHistory(_searchHistory);
    notifyListeners();
  }

  // توليد الاقتراحات
  void _generateSuggestions() {
    Set<String> suggestionSet = {};

    // إضافة أسماء المنتجات الشائعة
    for (final product in _allProducts) {
      suggestionSet.add(product.name);
      if (product.brand != null) {
        suggestionSet.add(product.brand!);
      }
      if (product.keywords != null) {
        suggestionSet.addAll(product.keywords!);
      }
    }

    // إضافة كلمات مفتاحية شائعة
    suggestionSet.addAll([
      'نظارات شمسية',
      'عدسات لاصقة',
      'نظارات طبية',
      'عدسات ملونة',
      'نظارات قراءة',
      'Ray-Ban',
      'Oakley',
      'نظارات رياضية',
      'عدسات يومية',
      'نظارات أطفال',
    ]);

    _suggestions = suggestionSet.toList();
    _suggestions.sort();
  }

  // مسح نتائج البحث
  void clearSearchResults() {
    _searchResults.clear();
    _currentQuery = '';
    notifyListeners();
  }
}

// فلاتر البحث
class SearchFilters {
  final String? categoryId;
  final String? brand;
  final double? minPrice;
  final double? maxPrice;
  final double? minRating;
  final bool inStockOnly;
  final bool onSaleOnly;
  final SortOption sortBy;

  const SearchFilters({
    this.categoryId,
    this.brand,
    this.minPrice,
    this.maxPrice,
    this.minRating,
    this.inStockOnly = false,
    this.onSaleOnly = false,
    this.sortBy = SortOption.relevance,
  });

  SearchFilters copyWith({
    String? categoryId,
    String? brand,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    bool? inStockOnly,
    bool? onSaleOnly,
    SortOption? sortBy,
  }) {
    return SearchFilters(
      categoryId: categoryId ?? this.categoryId,
      brand: brand ?? this.brand,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      minRating: minRating ?? this.minRating,
      inStockOnly: inStockOnly ?? this.inStockOnly,
      onSaleOnly: onSaleOnly ?? this.onSaleOnly,
      sortBy: sortBy ?? this.sortBy,
    );
  }

  bool get hasActiveFilters {
    return categoryId != null ||
        brand != null ||
        minPrice != null ||
        maxPrice != null ||
        minRating != null ||
        inStockOnly ||
        onSaleOnly;
  }
}

enum SortOption {
  relevance, // الصلة
  priceAsc, // السعر تصاعدي
  priceDesc, // السعر تنازلي
  rating, // التقييم
  newest, // الأحدث
  popular, // الأكثر شعبية
}
