import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
// import '../../services/app_state.dart'; // غير مستخدم حالياً
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import 'admin_accounts_page.dart';
import 'brands_management_page.dart';
import 'products_management_page.dart';
import 'categories_management_page.dart';
import 'orders_management_page.dart';
import 'users_management_page.dart';
import '../debug/debug_orders_page.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  // final AppState _appState = AppState(); // غير مستخدم حالياً
  Map<String, num> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      // جلب الإحصائيات الحقيقية من Firestore
      final futures = await Future.wait([
        FirestoreDataService.getProducts(),
        FirestoreDataService.getCategories(),
        FirestoreDataService.getOrders(),
        FirestoreDataService.getUsers(),
      ]);

      final products = futures[0] as List;
      final categories = futures[1] as List;
      final orders = futures[2] as List;
      final users = futures[3] as List;

      setState(() {
        _stats = {
          'products': products.length,
          'categories': categories.length,
          'orders': orders.length,
          'users': users.length,
          'revenue': _calculateRevenue(orders), // حساب الإيرادات من الطلبات
        };
        _isLoading = false;
      });

      if (kDebugMode) {
        print(
          '📊 إحصائيات محدثة: ${products.length} منتج، ${categories.length} فئة، ${orders.length} طلب، ${users.length} مستخدم',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الإحصائيات: $e');
      }
      // في حالة الخطأ، جرب البيانات المحلية
      try {
        final products = await ApiService.getProducts();
        final categories = await ApiService.getCategories();
        final orders = await ApiService.getOrders();

        setState(() {
          _stats = {
            'products': products.length,
            'categories': categories.length,
            'orders': orders.length,
            'users': 0,
            'revenue': 0,
          };
          _isLoading = false;
        });
      } catch (e2) {
        setState(() {
          _stats = {
            'products': 0,
            'categories': 0,
            'orders': 0,
            'users': 0,
            'revenue': 0,
          };
          _isLoading = false;
        });
      }
    }
  }

  double _calculateRevenue(List orders) {
    double total = 0;
    for (var order in orders) {
      if (order is Map && order['total'] != null) {
        total += (order['total'] as num).toDouble();
      }
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('لوحة تحكم الإدارة'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadStats,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // إحصائيات سريعة
                    _buildStatsCards(),

                    const SizedBox(height: 24),

                    // قائمة الإدارة
                    _buildManagementGrid(),

                    const SizedBox(height: 24),

                    // الطلبات الأخيرة
                    _buildRecentOrders(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات سريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'المنتجات',
                '${_stats['products'] ?? 0}',
                Icons.inventory,
                AppColors.primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'الفئات',
                '${_stats['categories'] ?? 0}',
                Icons.category,
                AppColors.accentColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'الطلبات',
                '${_stats['orders'] ?? 0}',
                Icons.shopping_cart,
                AppColors.successColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'المستخدمين',
                '${_stats['users'] ?? 0}',
                Icons.people,
                AppColors.warningColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementGrid() {
    final managementItems = [
      {
        'title': 'إدارة المنتجات',
        'icon': Icons.inventory,
        'color': AppColors.primaryColor,
        'page': const ProductsManagementPage(),
      },
      {
        'title': 'إدارة الفئات',
        'icon': Icons.category,
        'color': AppColors.accentColor,
        'page': const CategoriesManagementPage(),
      },
      {
        'title': 'إدارة البراندات',
        'icon': Icons.branding_watermark,
        'color': AppColors.frameGold,
        'page': const BrandsManagementPage(),
      },
      {
        'title': 'إدارة الطلبات',
        'icon': Icons.shopping_cart,
        'color': AppColors.successColor,
        'page': const OrdersManagementPage(),
      },
      {
        'title': 'إدارة المستخدمين',
        'icon': Icons.people,
        'color': AppColors.warningColor,
        'page': const UsersManagementPage(),
      },
      {
        'title': 'الحسابات الإدارية',
        'icon': Icons.admin_panel_settings,
        'color': AppColors.errorColor,
        'page': const AdminAccountsPage(),
      },
      {
        'title': 'تشخيص الطلبات',
        'icon': Icons.bug_report,
        'color': AppColors.warningColor,
        'page': const DebugOrdersPage(),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إدارة النظام',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: managementItems.length,
          itemBuilder: (context, index) {
            final item = managementItems[index];
            return _buildManagementCard(
              item['title'] as String,
              item['icon'] as IconData,
              item['color'] as Color,
              page: item['page'] as Widget?,
              action: item['action'] as VoidCallback?,
            );
          },
        ),
      ],
    );
  }

  Widget _buildManagementCard(
    String title,
    IconData icon,
    Color color, {
    Widget? page,
    VoidCallback? action,
  }) {
    return InkWell(
      onTap: () {
        if (page != null) {
          Navigator.of(
            context,
          ).push(MaterialPageRoute(builder: (context) => page));
        } else if (action != null) {
          action();
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowColor.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 32),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentOrders() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الطلبات الأخيرة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryText,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const OrdersManagementPage(),
                  ),
                );
              },
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadowColor.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Center(
            child: Text(
              'لا توجد طلبات حديثة',
              style: TextStyle(color: AppColors.secondaryText),
            ),
          ),
        ),
      ],
    );
  }
}
