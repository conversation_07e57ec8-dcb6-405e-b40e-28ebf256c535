import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة التفاعلية - تحاكي Google Sign-In بطريقة تفاعلية
class AuthService {
  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google التفاعلي (بدون Google Cloud Console)
  static Future<AuthResult> signInWithGoogle(BuildContext context) async {
    try {
      if (kDebugMode) {
        print('🔄 بدء تسجيل الدخول بـ Google التفاعلي...');
      }

      // عرض نافذة تسجيل الدخول التفاعلية
      final result = await _showGoogleSignInDialog(context);
      
      if (result == null) {
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      if (kDebugMode) {
        print('✅ تم تسجيل الدخول بنجاح!');
        print('📧 البريد الإلكتروني: ${result['email']}');
        print('👤 الاسم: ${result['name']}');
      }

      // إنشاء مستخدم باستخدام البيانات المدخلة
      final userData = user_model.User(
        id: 'google_${DateTime.now().millisecondsSinceEpoch}',
        email: result['email']!,
        firstName: result['firstName']!,
        lastName: result['lastName']!,
        phone: null,
        isEmailVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ البيانات في النظام المحاكي
      await FirebaseMockService.saveGoogleUser(userData, {
        'googleId': 'interactive_${DateTime.now().millisecondsSinceEpoch}',
        'accessToken': 'interactive_access_token',
        'idToken': 'interactive_id_token',
        'photoUrl': result['photoUrl'],
        'platform': kIsWeb ? 'web' : 'mobile',
        'signInMethod': 'google_interactive',
        'signInTime': DateTime.now().toIso8601String(),
        'userAgent': 'interactive_signin',
        'note': 'تسجيل دخول تفاعلي بدون Google Cloud Console',
      });

      if (kDebugMode) {
        print('✅ تم حفظ البيانات في النظام المحاكي');
        print('🎉 مرحباً ${userData.firstName}!');
      }

      return AuthResult.success(userData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول التفاعلي: $e');
      }
      return AuthResult.failure('حدث خطأ في تسجيل الدخول: $e');
    }
  }

  /// عرض نافذة تسجيل الدخول التفاعلية
  static Future<Map<String, String>?> _showGoogleSignInDialog(BuildContext context) async {
    final emailController = TextEditingController();
    final firstNameController = TextEditingController();
    final lastNameController = TextEditingController();

    return showDialog<Map<String, String>>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Image.network(
                'https://developers.google.com/identity/images/g-logo.png',
                width: 20,
                height: 20,
                errorBuilder: (context, error, stackTrace) => const Icon(Icons.account_circle, color: Colors.blue),
              ),
              const SizedBox(width: 8),
              const Text('تسجيل الدخول بـ Google'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'أدخل بياناتك الشخصية لمحاكاة تسجيل الدخول بـ Google',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    hintText: '<EMAIL>',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: firstNameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم الأول',
                    hintText: 'أحمد',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: lastNameController,
                  decoration: const InputDecoration(
                    labelText: 'الاسم الأخير',
                    hintText: 'محمد',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person_outline),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'هذه محاكاة لتسجيل الدخول بـ Google. أدخل بياناتك الحقيقية للحصول على تجربة واقعية.',
                          style: TextStyle(fontSize: 12, color: Colors.blue),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final email = emailController.text.trim();
                final firstName = firstNameController.text.trim();
                final lastName = lastNameController.text.trim();

                if (email.isEmpty || firstName.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى ملء البريد الإلكتروني والاسم الأول على الأقل'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (!email.contains('@')) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال بريد إلكتروني صحيح'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop({
                  'email': email,
                  'firstName': firstName,
                  'lastName': lastName.isEmpty ? '' : lastName,
                  'name': '$firstName ${lastName.isEmpty ? '' : lastName}'.trim(),
                  'photoUrl': 'https://lh3.googleusercontent.com/a/default-user=s96-c',
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('تسجيل الدخول'),
            ),
          ],
        );
      },
    );
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      if (kDebugMode) {
        print('🔄 تسجيل الخروج...');
      }
      
      // تسجيل الخروج من النظام المحاكي
      final result = await FirebaseMockService.signOut();
      
      if (kDebugMode) {
        print('✅ تم تسجيل الخروج بنجاح');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الخروج: $e');
      }
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// الحصول على معلومات مفصلة عن المستخدم الحالي
  static Map<String, dynamic>? get currentUserDetails {
    final user = currentUser;
    
    if (user == null) return null;
    
    return {
      'user': user,
      'isGoogleSignIn': user.id.startsWith('google_'),
      'email': user.email,
      'name': '${user.firstName} ${user.lastName}',
      'isEmailVerified': user.isEmailVerified,
      'signInTime': user.updatedAt,
    };
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
