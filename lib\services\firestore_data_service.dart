import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../models/category.dart' as category_model;
import 'firebase_real_service.dart';
import 'firebase_web_service.dart';
import 'firebase_service.dart';

/// دالة مساعدة للطباعة في وضع التطوير فقط
void _debugLog(String message) {
  if (kDebugMode) {
    debugPrint('📊 Firestore Data: $message');
  }
}

/// خدمة البيانات باستخدام Firestore - مشاركة البيانات بين جميع المستخدمين
class FirestoreDataService {
  static FirebaseFirestore? get _firestore => FirebaseRealService.firestore;

  // ==================== إدارة المنتجات ====================

  /// جلب قائمة المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      if (kDebugMode) {
        debugPrint(
          '🔄 [${DateTime.now()}] محاولة جلب المنتجات من Firestore...',
        );
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        if (kDebugMode) {
          debugPrint('🌐 [${DateTime.now()}] استخدام Firebase Web Service...');
        }
        final webProducts = await FirebaseWebService.getProducts();
        if (kDebugMode) {
          debugPrint(
            '🌐 [${DateTime.now()}] تم استلام ${webProducts.length} منتج من Firebase Web Service',
          );

          for (int i = 0; i < webProducts.length && i < 3; i++) {
            debugPrint(
              '🌐 منتج ${i + 1}: ${webProducts[i].name} (${webProducts[i].id})',
            );
          }
        }

        return webProducts;
      }

      // للموبايل: استخدم Firebase Real Service
      if (kDebugMode) {
        debugPrint('📱 [${DateTime.now()}] استخدام Firebase Real Service...');
      }
      final productsData = await FirebaseRealService.getProducts();
      final products = productsData
          .map((data) => Product.fromJson(data))
          .toList();

      if (kDebugMode) {
        debugPrint(
          '📱 [${DateTime.now()}] تم استلام ${products.length} منتج من Firebase Real Service',
        );
      }

      return products;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات من Firestore: $e');
        _debugLog('❌ نوع الخطأ: ${e.runtimeType}');
      }
      return [];
    }
  }

  /// إضافة منتج جديد إلى Firestore
  static Future<void> addProduct(Product product) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة إضافة منتج إلى Firestore: ${product.name}');
        _debugLog('🔄 معرف المنتج: ${product.id}');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لإضافة المنتج...');
        final success = await FirebaseWebService.addProduct(product);
        if (!success) {
          throw Exception('فشل في إضافة المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لإضافة المنتج...');
      final success = await FirebaseRealService.addProduct(product.toJson());
      if (!success) {
        throw Exception('فشل في إضافة المنتج عبر Firebase Real Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم إضافة المنتج بنجاح: ${product.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة المنتج إلى Firestore: $e');
        _debugLog('❌ نوع الخطأ: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  /// تعديل منتج في Firestore
  static Future<void> updateProduct(Product product) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة تعديل منتج في Firestore: ${product.name}');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لتعديل المنتج...');
        final success = await FirebaseWebService.updateProduct(product);
        if (!success) {
          throw Exception('فشل في تعديل المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لتعديل المنتج...');
      final success = await FirebaseRealService.updateProduct(product.id, product.toJson());
      if (!success) {
        throw Exception('فشل في تعديل المنتج عبر Firebase Real Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم تعديل المنتج بنجاح: ${product.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تعديل المنتج في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف منتج من Firestore
  static Future<void> deleteProduct(String productId) async {
    try {
      if (kDebugMode) {
        _debugLog('🔄 محاولة حذف منتج من Firestore: $productId');
      }

      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لحذف المنتج...');
        final success = await FirebaseWebService.deleteProduct(productId);
        if (!success) {
          throw Exception('فشل في حذف المنتج عبر Firebase Web Service');
        }
        return;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لحذف المنتج...');
      final success = await FirebaseRealService.deleteProduct(productId);
      if (!success) {
        throw Exception('فشل في حذف المنتج عبر Firebase Real Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم حذف المنتج بنجاح: $productId');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف المنتج من Firestore: $e');
      }
      rethrow;
    }
  }

  /// البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    final products = await getProducts();

    if (query.isEmpty) {
      return products;
    }

    return products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          (product.brand?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// جلب المنتجات حسب الفئة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات حسب الفئة: $e');
      }
      return [];
    }
  }

  /// جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المنتجات المميزة: $e');
      }
      return [];
    }
  }

  // ==================== إدارة الفئات ====================

  /// جلب قائمة الفئات من Firestore
  static Future<List<category_model.Category>> getCategories() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب الفئات...');
        final categoriesData = await FirebaseWebService.getCategories();

        final categories = categoriesData
            .map((data) => category_model.Category.fromJson(data))
            .toList();

        if (kDebugMode) {
          _debugLog(
            '📂 تم جلب ${categories.length} فئة من Firebase Web Service',
          );
        }

        return categories;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لجلب الفئات...');
      final categoriesData = await FirebaseRealService.getCategories();
      final categories = categoriesData
          .map((data) => category_model.Category.fromJson(data))
          .toList();

      if (kDebugMode) {
        _debugLog('📂 تم جلب ${categories.length} فئة من Firebase Real Service');
      }

      return categories;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب الفئات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة فئة جديدة إلى Firestore
  static Future<void> addCategory(category_model.Category category) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لإضافة الفئة...');
        final success = await FirebaseWebService.addCategory(category.toJson());
        if (!success) {
          throw Exception('فشل في إضافة الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .set(category.toJson());

      if (kDebugMode) {
        _debugLog('✅ تم إضافة الفئة إلى Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة الفئة إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث فئة في Firestore
  static Future<void> updateCategory(category_model.Category category) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لتعديل الفئة...');
        final success = await FirebaseWebService.updateCategory(
          category.toJson(),
        );
        if (!success) {
          throw Exception('فشل في تعديل الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .update(category.toJson());

      if (kDebugMode) {
        _debugLog('✅ تم تحديث الفئة في Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث الفئة في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف فئة من Firestore
  static Future<void> deleteCategory(String categoryId) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لحذف الفئة...');
        final success = await FirebaseWebService.deleteCategory(categoryId);
        if (!success) {
          throw Exception('فشل في حذف الفئة عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('categories').doc(categoryId).delete();

      if (kDebugMode) {
        _debugLog('✅ تم حذف الفئة من Firestore: $categoryId');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف الفئة من Firestore: $e');
      }
      rethrow;
    }
  }

  // ==================== مزامنة البيانات ====================

  /// مزامنة البيانات المحلية مع Firestore
  static Future<void> syncLocalDataToFirestore() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) _debugLog('⚠️ Firestore غير متاح للمزامنة');
        return;
      }

      if (kDebugMode) {
        _debugLog('🔄 بدء مزامنة البيانات المحلية مع Firestore...');
      }

      // مزامنة المنتجات المحلية
      // يمكن إضافة هذا لاحقاً إذا احتجنا

      if (kDebugMode) {
        _debugLog('✅ تمت مزامنة البيانات مع Firestore');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في مزامنة البيانات: $e');
      }
    }
  }

  /// التحقق من اتصال Firestore
  static Future<bool> isFirestoreAvailable() async {
    try {
      if (_firestore == null) return false;
      await _firestore!.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// === إدارة الطلبات ===

  /// جلب جميع الطلبات من Firestore
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب الطلبات...');
        return await FirebaseWebService.getOrders();
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لجلب الطلبات...');
      return await FirebaseRealService.getOrders();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب الطلبات من Firestore: $e');
      }
      return [];
    }
  }

  /// تحديث حالة الطلب في Firestore
  static Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لتحديث حالة الطلب...');
        return await FirebaseWebService.updateOrderStatus(orderId, status);
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return false;
      }

      await _firestore!.collection('orders').doc(orderId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        _debugLog('✅ تم تحديث حالة الطلب $orderId إلى $status');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث حالة الطلب: $e');
      }
      return false;
    }
  }

  /// جلب المستخدمين من Firestore
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب المستخدمين...');
        return await FirebaseWebService.getUsers();
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لجلب المستخدمين...');
      return await FirebaseRealService.getUsers();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب المستخدمين من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة طلب جديد إلى Firestore
  static Future<void> addOrder(Map<String, dynamic> orderData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لإضافة الطلب...');
        final success = await FirebaseWebService.addOrder(orderData);
        if (!success) {
          throw Exception('فشل في إضافة الطلب عبر Firebase Web Service');
        }
        return;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لإضافة الطلب...');
      final success = await FirebaseRealService.addOrder(orderData);
      if (!success) {
        throw Exception('فشل في إضافة الطلب عبر Firebase Real Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم إضافة الطلب بنجاح: ${orderData['id']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة الطلب إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// === إدارة البراندات ===

  /// جلب جميع البراندات من Firestore
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لجلب البراندات...');
        return await FirebaseWebService.getBrands();
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('� استخدام Firebase Real Service لجلب البراندات...');
      return await FirebaseRealService.getBrands();
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في جلب البراندات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة براند جديد
  static Future<void> addBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لإضافة البراند...');
        final success = await FirebaseWebService.addBrand(brandData);
        if (!success) {
          throw Exception('فشل في إضافة البراند عبر Firebase Web Service');
        }
        return;
      }

      // للموبايل: استخدم Firebase Real Service
      _debugLog('📱 استخدام Firebase Real Service لإضافة البراند...');
      final success = await FirebaseRealService.addBrand(brandData);
      if (!success) {
        throw Exception('فشل في إضافة البراند عبر Firebase Real Service');
      }

      if (kDebugMode) {
        _debugLog('✅ تم إضافة البراند بنجاح: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في إضافة البراند إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث براند موجود
  static Future<void> updateBrand(Map<String, dynamic> brandData) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لتحديث البراند...');
        final success = await FirebaseWebService.updateBrand(brandData);
        if (!success) {
          throw Exception('فشل في تحديث البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('brands')
          .doc(brandData['id'])
          .update(brandData);

      if (kDebugMode) {
        _debugLog('✅ تم تحديث البراند في Firestore: ${brandData['name']}');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في تحديث البراند في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف براند
  static Future<void> deleteBrand(String brandId) async {
    try {
      // للويب: استخدم Firebase Web Service
      if (kIsWeb) {
        _debugLog('🌐 استخدام Firebase Web Service لحذف البراند...');
        final success = await FirebaseWebService.deleteBrand(brandId);
        if (!success) {
          throw Exception('فشل في حذف البراند عبر Firebase Web Service');
        }
        return;
      }

      // للمنصات الأخرى: استخدم Firebase الحقيقي
      if (_firestore == null) {
        if (kDebugMode) {
          _debugLog('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('brands').doc(brandId).delete();

      if (kDebugMode) {
        _debugLog('✅ تم حذف البراند من Firestore: $brandId');
      }
    } catch (e) {
      if (kDebugMode) {
        _debugLog('❌ خطأ في حذف البراند من Firestore: $e');
      }
      rethrow;
    }
  }
}
