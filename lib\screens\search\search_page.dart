import 'package:flutter/material.dart';
import '../../app_properties.dart';
// import '../../services/app_state.dart'; // غير مستخدم حالياً
import '../../services/storage_service.dart';
import '../../models/product.dart';
import '../product/product_page.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final _searchController = TextEditingController();
  // final AppState _appState = AppState(); // غير مستخدم حالياً
  bool _isSearching = false;
  List<String> _searchHistory = [];
  List<Product> _searchResults = [];
  String _selectedFilter = 'الكل';

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSearchHistory() async {
    final history = await StorageService.loadSearchHistory();
    setState(() {
      _searchHistory = history;
    });
  }

  Future<void> _loadInitialData() async {
    // تحميل المنتجات من الخدمة الوهمية
    setState(() {
      _isSearching = true;
    });

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      setState(() {
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('البحث'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر - ثابت في الأعلى
          Container(
            color: AppColors.scaffoldBackground,
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Column(
              children: [
                // شريط البحث
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(
                      AppDimensions.borderRadiusLarge,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن النظارات والعدسات...',
                      prefixIcon: const Icon(
                        Icons.search,
                        color: AppColors.primaryColor,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _searchController.clear();
                                });
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingMedium,
                        vertical: AppDimensions.paddingMedium,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {});
                    },
                    onSubmitted: (value) {
                      _performSearch(value);
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // فلاتر البحث
                _buildSearchFilters(),
              ],
            ),
          ),

          // المحتوى القابل للتمرير
          Expanded(
            child: _isSearching
                ? const Center(child: CircularProgressIndicator())
                : _searchController.text.isEmpty
                ? _buildSearchSuggestions()
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchFilters() {
    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', _selectedFilter == 'الكل'),
          _buildFilterChip('نظارات طبية', _selectedFilter == 'نظارات طبية'),
          _buildFilterChip('نظارات شمسية', _selectedFilter == 'نظارات شمسية'),
          _buildFilterChip('عدسات لاصقة', _selectedFilter == 'عدسات لاصقة'),
          _buildFilterChip('إكسسوارات', _selectedFilter == 'إكسسوارات'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = label;
          });
          _applyFilter();
        },
        backgroundColor: AppColors.white,
        selectedColor: AppColors.primaryColor.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primaryColor,
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_searchHistory.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'البحث السابق',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () async {
                    await StorageService.clearSearchHistory();
                    await _loadSearchHistory();
                  },
                  child: const Text('مسح الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _searchHistory
                  .map((query) => _buildSuggestionChip(query))
                  .toList(),
            ),
            const SizedBox(height: 30),
          ],
          Text(
            'البحث الشائع',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildSuggestionChip('نظارات راي بان'),
              _buildSuggestionChip('عدسات يومية'),
              _buildSuggestionChip('نظارات قراءة'),
              _buildSuggestionChip('نظارات شمسية'),
              _buildSuggestionChip('عدسات ملونة'),
              _buildSuggestionChip('نظارات أطفال'),
            ],
          ),
          const SizedBox(height: 30),
          Text(
            'الفئات الشائعة',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildCategoryGrid(),
          const SizedBox(height: 20), // مساحة إضافية في النهاية
        ],
      ),
    );
  }

  Widget _buildSuggestionChip(String suggestion) {
    return GestureDetector(
      onTap: () {
        _searchController.text = suggestion;
        _performSearch(suggestion);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.lightGrey,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(suggestion, style: Theme.of(context).textTheme.bodyMedium),
      ),
    );
  }

  Widget _buildCategoryGrid() {
    final categories = [
      {
        'name': 'نظارات طبية',
        'icon': Icons.visibility,
        'color': AppColors.primaryColor,
      },
      {
        'name': 'نظارات شمسية',
        'icon': Icons.wb_sunny,
        'color': AppColors.frameGold,
      },
      {
        'name': 'عدسات لاصقة',
        'icon': Icons.remove_red_eye,
        'color': AppColors.lensBlue,
      },
      {
        'name': 'إكسسوارات',
        'icon': Icons.shopping_bag,
        'color': AppColors.visionGreen,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return GestureDetector(
          onTap: () {
            _searchController.text = category['name'] as String;
            _performSearch(category['name'] as String);
          },
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusLarge,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  category['icon'] as IconData,
                  size: 40,
                  color: category['color'] as Color,
                ),
                const SizedBox(height: 8),
                Text(
                  category['name'] as String,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchResults() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'نتائج البحث عن "${_searchController.text}"',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              if (_searchResults.isNotEmpty)
                Text(
                  '${_searchResults.length} منتج',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          if (_searchResults.isEmpty)
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.4,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 80,
                      color: AppColors.grey.withValues(alpha: 0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد نتائج',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'جرب البحث بكلمات مختلفة أو غير الفلتر',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                return _buildProductCard(_searchResults[index]);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductPage(product: product),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
          child: Row(
            children: [
              // صورة المنتج
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.lightGrey,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.borderRadiusMedium,
                  ),
                ),
                child: Icon(
                  _getProductIcon(product.type),
                  size: 30,
                  color: AppColors.primaryColor,
                ),
              ),

              const SizedBox(width: 16),

              // معلومات المنتج
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.categoryName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          size: 14,
                          color: AppColors.frameGold,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          product.rating.toStringAsFixed(1),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '(${product.reviewsCount})',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppColors.secondaryText),
                        ),
                        const Spacer(),
                        Text(
                          AppConstants.formatPrice(product.price),
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getProductIcon(ProductType type) {
    switch (type) {
      case ProductType.eyeglasses:
        return Icons.visibility;
      case ProductType.sunglasses:
        return Icons.wb_sunny;
      case ProductType.contactLenses:
        return Icons.remove_red_eye;
      case ProductType.readingGlasses:
        return Icons.menu_book;
      case ProductType.accessories:
        return Icons.shopping_bag;
      case ProductType.solutions:
        return Icons.water_drop;
    }
  }

  void _performSearch(String query) async {
    if (query.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    // حفظ في تاريخ البحث
    await StorageService.addToSearchHistory(query);
    await _loadSearchHistory();

    // محاكاة البحث
    await Future.delayed(const Duration(milliseconds: 800));

    if (mounted) {
      // محاكاة نتائج البحث
      final mockResults = _getMockSearchResults(query);

      setState(() {
        _searchResults = mockResults;
        _isSearching = false;
      });
    }
  }

  void _applyFilter() {
    if (_searchController.text.isNotEmpty) {
      _performSearch(_searchController.text);
    }
  }

  List<Product> _getMockSearchResults(String query) {
    // محاكاة نتائج البحث بناءً على الاستعلام والفلتر
    final allProducts = _getMockProducts();

    List<Product> filtered = allProducts
        .where(
          (product) =>
              product.name.toLowerCase().contains(query.toLowerCase()) ||
              product.description.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();

    // تطبيق فلتر الفئة
    if (_selectedFilter != 'الكل') {
      filtered = filtered.where((product) {
        switch (_selectedFilter) {
          case 'نظارات طبية':
            return product.categoryId == 'medical_glasses';
          case 'نظارات شمسية':
            return product.categoryId == 'sunglasses';
          case 'عدسات لاصقة':
            return product.categoryId == 'contact_lenses';
          case 'إكسسوارات':
            return product.categoryId == 'accessories';
          default:
            return true;
        }
      }).toList();
    }

    return filtered;
  }

  List<Product> _getMockProducts() {
    final now = DateTime.now();
    return [
      Product(
        id: '1',
        name: 'نظارة راي بان كلاسيك',
        description: 'نظارة طبية عالية الجودة مع إطار معدني متين',
        price: 225000.0,
        image: 'assets/images/product1.jpg',
        images: ['assets/images/product1.jpg'],
        categoryId: 'medical_glasses',
        categoryName: 'نظارات طبية',
        rating: 4.5,
        reviewsCount: 120,
        inStock: true,
        stockQuantity: 25,
        specifications: {
          'frameWidth': '140mm',
          'lensWidth': '52mm',
          'bridgeWidth': '18mm',
          'material': 'معدن',
        },
        type: ProductType.eyeglasses,
        brand: 'Ray-Ban',
        availableColors: ['أسود', 'بني', 'ذهبي'],
        availableSizes: ['صغير', 'متوسط', 'كبير'],
        createdAt: now,
        updatedAt: now,
        isFeatured: true,
      ),
      Product(
        id: '2',
        name: 'عدسات لاصقة يومية',
        description:
            'عدسات مريحة للاستخدام اليومي مع حماية من الأشعة فوق البنفسجية',
        price: 60000.0,
        image: 'assets/images/product2.jpg',
        images: ['assets/images/product2.jpg'],
        categoryId: 'contact_lenses',
        categoryName: 'عدسات لاصقة',
        rating: 4.8,
        reviewsCount: 89,
        inStock: true,
        stockQuantity: 50,
        specifications: {
          'diameter': '14.2mm',
          'baseCurve': '8.6mm',
          'waterContent': '58%',
          'wearingSchedule': 'يومي',
        },
        type: ProductType.contactLenses,
        brand: 'Acuvue',
        availableColors: ['شفاف', 'أزرق', 'أخضر'],
        availablePowers: ['-1.00', '-2.50', '-3.00', '-4.00'],
        createdAt: now,
        updatedAt: now,
        isNew: true,
      ),
      Product(
        id: '3',
        name: 'نظارة شمسية رياضية',
        description:
            'نظارة شمسية مثالية للرياضة مع حماية 100% من الأشعة فوق البنفسجية',
        price: 180000.0,
        image: 'assets/images/product3.jpg',
        images: ['assets/images/product3.jpg'],
        categoryId: 'sunglasses',
        categoryName: 'نظارات شمسية',
        rating: 4.3,
        reviewsCount: 67,
        inStock: true,
        stockQuantity: 15,
        specifications: {
          'frameWidth': '145mm',
          'lensWidth': '60mm',
          'uvProtection': '100%',
          'material': 'بلاستيك مقوى',
        },
        type: ProductType.sunglasses,
        brand: 'Oakley',
        availableColors: ['أسود', 'أزرق', 'أحمر'],
        availableSizes: ['متوسط', 'كبير'],
        createdAt: now,
        updatedAt: now,
        isOnSale: true,
        discountPercentage: 15.0,
      ),
    ];
  }
}
