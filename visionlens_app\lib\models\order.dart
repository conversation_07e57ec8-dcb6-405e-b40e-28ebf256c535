class Order {
  final String id;
  final DateTime date;
  final OrderStatus status;
  final double total;
  final List<OrderItem> items;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? estimatedDelivery;
  final String? notes;

  const Order({
    required this.id,
    required this.date,
    required this.status,
    required this.total,
    required this.items,
    this.shippingAddress,
    this.trackingNumber,
    this.estimatedDelivery,
    this.notes,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => OrderStatus.processing,
      ),
      total: (json['total'] ?? 0).toDouble(),
      items:
          (json['items'] as List<dynamic>?)
              ?.map((item) => OrderItem.fromJson(item))
              .toList() ??
          [],
      shippingAddress: json['shippingAddress'],
      trackingNumber: json['trackingNumber'],
      estimatedDelivery: json['estimatedDelivery'] != null
          ? DateTime.parse(json['estimatedDelivery'])
          : null,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'status': status.toString().split('.').last,
      'total': total,
      'items': items.map((item) => item.toJson()).toList(),
      'shippingAddress': shippingAddress,
      'trackingNumber': trackingNumber,
      'estimatedDelivery': estimatedDelivery?.toIso8601String(),
      'notes': notes,
    };
  }

  // نسخة محدثة من الطلب
  Order copyWith({
    String? id,
    DateTime? date,
    OrderStatus? status,
    double? total,
    List<OrderItem>? items,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? estimatedDelivery,
    String? notes,
  }) {
    return Order(
      id: id ?? this.id,
      date: date ?? this.date,
      status: status ?? this.status,
      total: total ?? this.total,
      items: items ?? this.items,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      notes: notes ?? this.notes,
    );
  }

  // حساب عدد العناصر
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  // التحقق من إمكانية الإلغاء
  bool get canCancel => status == OrderStatus.processing;

  // التحقق من إمكانية التتبع
  bool get canTrack =>
      trackingNumber != null &&
      (status == OrderStatus.shipped || status == OrderStatus.delivered);

  // التحقق من إمكانية إعادة الطلب
  bool get canReorder => status == OrderStatus.delivered;
}

class OrderItem {
  final String productId;
  final String productName;
  final String imageUrl;
  final int quantity;
  final double price;
  final String? selectedColor;
  final String? selectedSize;
  final Map<String, dynamic>? customizations;

  const OrderItem({
    required this.productId,
    required this.productName,
    required this.imageUrl,
    required this.quantity,
    required this.price,
    this.selectedColor,
    this.selectedSize,
    this.customizations,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      quantity: json['quantity'] ?? 1,
      price: (json['price'] ?? 0).toDouble(),
      selectedColor: json['selectedColor'],
      selectedSize: json['selectedSize'],
      customizations: json['customizations'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'price': price,
      'selectedColor': selectedColor,
      'selectedSize': selectedSize,
      'customizations': customizations,
    };
  }

  // المجموع الفرعي للعنصر
  double get subtotal => price * quantity;

  // نسخة محدثة من عنصر الطلب
  OrderItem copyWith({
    String? productId,
    String? productName,
    String? imageUrl,
    int? quantity,
    double? price,
    String? selectedColor,
    String? selectedSize,
    Map<String, dynamic>? customizations,
  }) {
    return OrderItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      selectedColor: selectedColor ?? this.selectedColor,
      selectedSize: selectedSize ?? this.selectedSize,
      customizations: customizations ?? this.customizations,
    );
  }
}

enum OrderStatus {
  processing, // قيد المعالجة
  confirmed, // مؤكد
  preparing, // قيد التحضير
  shipped, // تم الشحن
  delivered, // تم التسليم
  cancelled, // ملغي
  returned, // مرتجع
}

enum PaymentMethod {
  cash, // الدفع عند الاستلام
  card, // بطاقة ائتمان
  wallet, // محفظة إلكترونية
  bankTransfer, // تحويل بنكي
}

enum ShippingMethod {
  standard, // شحن عادي
  express, // شحن سريع
  overnight, // شحن خلال الليل
  pickup, // استلام من المتجر
}

// معلومات الشحن
class ShippingInfo {
  final String address;
  final String city;
  final String country;
  final String postalCode;
  final String? phone;
  final String? notes;
  final ShippingMethod method;
  final double cost;
  final DateTime? estimatedDelivery;

  const ShippingInfo({
    required this.address,
    required this.city,
    required this.country,
    required this.postalCode,
    this.phone,
    this.notes,
    required this.method,
    required this.cost,
    this.estimatedDelivery,
  });

  factory ShippingInfo.fromJson(Map<String, dynamic> json) {
    return ShippingInfo(
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      postalCode: json['postalCode'] ?? '',
      phone: json['phone'],
      notes: json['notes'],
      method: ShippingMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => ShippingMethod.standard,
      ),
      cost: (json['cost'] ?? 0).toDouble(),
      estimatedDelivery: json['estimatedDelivery'] != null
          ? DateTime.parse(json['estimatedDelivery'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'phone': phone,
      'notes': notes,
      'method': method.toString().split('.').last,
      'cost': cost,
      'estimatedDelivery': estimatedDelivery?.toIso8601String(),
    };
  }

  // العنوان الكامل
  String get fullAddress => '$address, $city, $country $postalCode';
}

// معلومات الدفع
class PaymentInfo {
  final PaymentMethod method;
  final PaymentStatus status;
  final double amount;
  final String? transactionId;
  final DateTime? paidAt;
  final String? cardLast4;
  final String? cardBrand;

  const PaymentInfo({
    required this.method,
    required this.status,
    required this.amount,
    this.transactionId,
    this.paidAt,
    this.cardLast4,
    this.cardBrand,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => PaymentMethod.cash,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      amount: (json['amount'] ?? 0).toDouble(),
      transactionId: json['transactionId'],
      paidAt: json['paidAt'] != null ? DateTime.parse(json['paidAt']) : null,
      cardLast4: json['cardLast4'],
      cardBrand: json['cardBrand'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'transactionId': transactionId,
      'paidAt': paidAt?.toIso8601String(),
      'cardLast4': cardLast4,
      'cardBrand': cardBrand,
    };
  }
}

enum PaymentStatus {
  pending, // في الانتظار
  paid, // مدفوع
  failed, // فشل
  refunded, // مسترد
}
