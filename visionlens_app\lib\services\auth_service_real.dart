import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_simple.dart' as user_model;

/// خدمة المصادقة الحقيقية مع Firebase
class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // الحصول على المستخدم الحالي
  static User? get currentFirebaseUser => _auth.currentUser;
  
  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => _auth.currentUser != null;

  // Stream للاستماع لتغييرات حالة المصادقة
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        final userData = await _getUserData(credential.user!.uid);
        return AuthResult.success(userData);
      } else {
        return AuthResult.failure('فشل في تسجيل الدخول');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getErrorMessage(e.code));
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google الحقيقي
  static Future<AuthResult> signInWithGoogle() async {
    try {
      print('🔄 بدء عملية تسجيل الدخول بـ Google...');
      
      // بدء عملية تسجيل الدخول
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        print('❌ تم إلغاء تسجيل الدخول من قبل المستخدم');
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      print('✅ تم اختيار حساب Google: ${googleUser.email}');

      // الحصول على تفاصيل المصادقة
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        print('❌ فشل في الحصول على رموز المصادقة');
        return AuthResult.failure('فشل في الحصول على رموز المصادقة');
      }

      print('✅ تم الحصول على رموز المصادقة');

      // إنشاء credential جديد
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      print('🔄 تسجيل الدخول إلى Firebase...');

      // تسجيل الدخول إلى Firebase
      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        print('✅ تم تسجيل الدخول بنجاح إلى Firebase');
        
        // التحقق من وجود المستخدم في قاعدة البيانات
        user_model.User? userData = await _getUserData(userCredential.user!.uid);

        // إذا لم يكن موجود، إنشاء حساب جديد
        if (userData == null) {
          print('🔄 إنشاء ملف تعريف جديد للمستخدم...');
          
          userData = user_model.User(
            id: userCredential.user!.uid,
            email: userCredential.user!.email!,
            firstName: userCredential.user!.displayName?.split(' ').first ?? 'مستخدم',
            lastName: userCredential.user!.displayName?.split(' ').skip(1).join(' ') ?? 'جديد',
            phone: userCredential.user!.phoneNumber,
            isEmailVerified: userCredential.user!.emailVerified,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await _createUserData(userData);
          print('✅ تم إنشاء ملف تعريف المستخدم');
        } else {
          // تحديث آخر تسجيل دخول
          await _updateUserData(userData.id, {
            'updatedAt': DateTime.now().toIso8601String(),
          });
          print('✅ تم تحديث بيانات المستخدم');
        }

        return AuthResult.success(userData);
      } else {
        print('❌ فشل في تسجيل الدخول إلى Firebase');
        return AuthResult.failure('فشل في تسجيل الدخول بـ Google');
      }
    } on FirebaseAuthException catch (e) {
      print('❌ خطأ Firebase Auth: ${e.code} - ${e.message}');
      return AuthResult.failure(_getErrorMessage(e.code));
    } catch (e) {
      print('❌ خطأ عام في تسجيل الدخول بـ Google: $e');
      return AuthResult.failure('حدث خطأ في تسجيل الدخول بـ Google: $e');
    }
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // إنشاء بيانات المستخدم في Firestore
        final userData = user_model.User(
          id: credential.user!.uid,
          email: email.trim(),
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          isEmailVerified: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _createUserData(userData);

        // إرسال رسالة تأكيد البريد الإلكتروني
        await credential.user!.sendEmailVerification();

        return AuthResult.success(userData);
      } else {
        return AuthResult.failure('فشل في إنشاء الحساب');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getErrorMessage(e.code));
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
      return true;
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني
  static Future<bool> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في إرسال رسالة التأكيد: $e');
      return false;
    }
  }

  /// إعادة تعيين كلمة المرور
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email.trim());
      return true;
    } catch (e) {
      print('خطأ في إرسال رسالة إعادة التعيين: $e');
      return false;
    }
  }

  // ==================== وظائف مساعدة ====================

  /// الحصول على بيانات المستخدم من Firestore
  static Future<user_model.User?> _getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return user_model.User.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      print('خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  /// إنشاء بيانات المستخدم في Firestore
  static Future<void> _createUserData(user_model.User userData) async {
    try {
      await _firestore.collection('users').doc(userData.id).set(userData.toJson());
    } catch (e) {
      print('خطأ في إنشاء بيانات المستخدم: $e');
      throw e;
    }
  }

  /// تحديث بيانات المستخدم في Firestore
  static Future<void> _updateUserData(String uid, Map<String, dynamic> data) async {
    try {
      await _firestore.collection('users').doc(uid).update(data);
    } catch (e) {
      print('خطأ في تحديث بيانات المستخدم: $e');
      throw e;
    }
  }

  /// الحصول على رسالة خطأ مفهومة
  static String _getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة جداً';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا الحساب';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'operation-not-allowed':
        return 'هذه العملية غير مسموحة';
      case 'requires-recent-login':
        return 'يتطلب تسجيل دخول حديث';
      case 'account-exists-with-different-credential':
        return 'يوجد حساب بنفس البريد الإلكتروني بطريقة تسجيل دخول مختلفة';
      case 'invalid-credential':
        return 'بيانات الاعتماد غير صحيحة';
      case 'credential-already-in-use':
        return 'بيانات الاعتماد مستخدمة بالفعل';
      default:
        return 'حدث خطأ غير متوقع: $errorCode';
    }
  }

  /// الحصول على المستخدم الحالي كنموذج User
  static Future<user_model.User?> getCurrentUser() async {
    final firebaseUser = _auth.currentUser;
    if (firebaseUser != null) {
      return await _getUserData(firebaseUser.uid);
    }
    return null;
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
