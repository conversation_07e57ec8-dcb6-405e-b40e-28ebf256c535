import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../app_properties.dart';
import '../../services/firestore_data_service.dart';
import '../../services/app_state.dart';

class DebugOrdersPage extends StatefulWidget {
  const DebugOrdersPage({super.key});

  @override
  State<DebugOrdersPage> createState() => _DebugOrdersPageState();
}

class _DebugOrdersPageState extends State<DebugOrdersPage> {
  List<Map<String, dynamic>> _allOrders = [];
  List<Map<String, dynamic>> _userOrders = [];
  bool _isLoading = true;
  final AppState _appState = AppState();

  @override
  void initState() {
    super.initState();
    _loadAllOrders();
  }

  Future<void> _loadAllOrders() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final currentUser = _appState.currentUser;
      if (kDebugMode) {
        print(
          '👤 المستخدم الحالي: ${currentUser?.email} (ID: ${currentUser?.id})',
        );
      }

      // جلب جميع الطلبات
      final ordersData = await FirestoreDataService.getOrders();

      if (kDebugMode) {
        print('📦 إجمالي الطلبات: ${ordersData.length}');
        for (var order in ordersData) {
          print('   طلب ${order['id']}:');
          print('      - العميل: ${order['customerEmail']}');
          print('      - المستخدم: ${order['userId']}');
          print('      - الحالة: ${order['status']}');
          print('      - التاريخ: ${order['createdAt']}');
          print('      - المجموع: ${order['total']}');
        }
      }

      // فلترة طلبات المستخدم الحالي
      List<Map<String, dynamic>> userOrders = [];
      if (currentUser != null) {
        userOrders = ordersData.where((order) {
          final orderEmail =
              order['customerEmail']?.toString().toLowerCase() ?? '';
          final orderUserId = order['userId']?.toString() ?? '';
          final currentEmail = currentUser.email.toLowerCase();
          final currentUserId = currentUser.id;

          return orderEmail == currentEmail || orderUserId == currentUserId;
        }).toList();
      }

      setState(() {
        _allOrders = ordersData;
        _userOrders = userOrders;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('📦 طلبات المستخدم الحالي: ${userOrders.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الطلبات: $e');
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('تشخيص الطلبات'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllOrders,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم
                  _buildUserInfo(),
                  const SizedBox(height: 20),

                  // إحصائيات
                  _buildStats(),
                  const SizedBox(height: 20),

                  // طلبات المستخدم الحالي
                  _buildUserOrdersSection(),
                  const SizedBox(height: 20),

                  // جميع الطلبات
                  _buildAllOrdersSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildUserInfo() {
    final currentUser = _appState.currentUser;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'معلومات المستخدم الحالي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          if (currentUser != null) ...[
            Text('البريد الإلكتروني: ${currentUser.email}'),
            Text('معرف المستخدم: ${currentUser.id}'),
            Text('الاسم: ${currentUser.fullName}'),
          ] else ...[
            const Text(
              'لا يوجد مستخدم مسجل دخول',
              style: TextStyle(color: AppColors.errorColor),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات الطلبات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الطلبات',
                  _allOrders.length.toString(),
                  AppColors.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'طلباتي',
                  _userOrders.length.toString(),
                  AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUserOrdersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلباتي (${_userOrders.length})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
          const SizedBox(height: 12),
          if (_userOrders.isEmpty) ...[
            const Text(
              'لا توجد طلبات للمستخدم الحالي',
              style: TextStyle(color: AppColors.secondaryText),
            ),
          ] else ...[
            ..._userOrders.map((order) => _buildOrderCard(order, true)),
          ],
        ],
      ),
    );
  }

  Widget _buildAllOrdersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'جميع الطلبات (${_allOrders.length})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          if (_allOrders.isEmpty) ...[
            const Text(
              'لا توجد طلبات في النظام',
              style: TextStyle(color: AppColors.secondaryText),
            ),
          ] else ...[
            ..._allOrders.map((order) => _buildOrderCard(order, false)),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order, bool isUserOrder) {
    final currentUser = _appState.currentUser;
    final isCurrentUserOrder =
        currentUser != null &&
        (order['customerEmail']?.toString().toLowerCase() ==
                currentUser.email.toLowerCase() ||
            order['userId']?.toString() == currentUser.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentUserOrder
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.scaffoldBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentUserOrder
              ? AppColors.success
              : AppColors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طلب #${order['id']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (isCurrentUserOrder)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'طلبي',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text('العميل: ${order['customerEmail'] ?? 'غير محدد'}'),
          Text('المستخدم: ${order['userId'] ?? 'غير محدد'}'),
          Text('الحالة: ${order['status'] ?? 'غير محدد'}'),
          Text('المجموع: ${order['total'] ?? 0} IQD'),
          Text('التاريخ: ${order['createdAt'] ?? 'غير محدد'}'),
        ],
      ),
    );
  }
}
