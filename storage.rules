rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // دالة للتحقق من أن المستخدم مسجل دخول
    function isAuthenticated() {
      return request.auth != null;
    }

    // دالة للتحقق من أن المستخدم مدير
    function isAdmin() {
      return request.auth != null &&
             firestore.exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }

    // دالة للتحقق من صحة نوع الملف
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }

    // دالة للتحقق من حجم الملف (أقل من 5 ميجابايت للصور العادية، 10 ميجابايت للمنتجات)
    function isValidSize() {
      return request.resource.size < 5 * 1024 * 1024;
    }

    // دالة للتحقق من حجم الملف للمنتجات (أكبر قليلاً)
    function isValidProductImageSize() {
      return request.resource.size < 10 * 1024 * 1024;
    }

    // دالة للتحقق من أن اسم الملف آمن
    function isValidFileName() {
      return resource.name.matches('[a-zA-Z0-9._-]+');
    }

    // دالة للتحقق من معدل الرفع (حماية من الإفراط) - محذوفة لعدم الاستخدام
    // function isWithinUploadLimit() {
    //   return request.time < resource.timeCreated + duration.value(1, 'h');
    // }

    // صور المنتجات - قراءة للجميع، كتابة للمدراء فقط مع حماية إضافية
    match /products/{productId}/{imageId} {
      allow read: if true;
      allow write: if isAdmin() &&
                   isValidImageType() &&
                   isValidProductImageSize() &&
                   isValidFileName();
      allow delete: if isAdmin();
    }

    // صور الفئات - قراءة للجميع، كتابة للمدراء فقط
    match /categories/{categoryId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور العلامات التجارية - قراءة للجميع، كتابة للمدراء فقط
    match /brands/{brandId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور المستخدمين - كل مستخدم يمكنه إدارة صوره فقط مع حماية إضافية
    match /users/{userId}/{imageId} {
      allow read: if isAuthenticated() && (request.auth.uid == userId || isAdmin());
      allow write: if isAuthenticated() &&
                   request.auth.uid == userId &&
                   isValidImageType() &&
                   isValidSize() &&
                   isValidFileName();
      allow delete: if isAuthenticated() &&
                    (request.auth.uid == userId || isAdmin());
    }

    // صور عامة - قراءة للجميع، كتابة للمدراء فقط
    match /public/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // مجلد مؤقت للرفع - للمستخدمين المسجلين فقط
    match /temp/{userId}/{imageId} {
      allow read, write, delete: if isAuthenticated() &&
                                 request.auth.uid == userId &&
                                 isValidImageType() && isValidSize();
    }

    // منع الوصول لأي مسارات أخرى
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
