class Order {
  final String id;
  final DateTime date;
  final OrderStatus status;
  final double total;
  final List<OrderItem> items;
  final String? shippingAddress;
  final String? trackingNumber;
  final DateTime? estimatedDelivery;
  final String? notes;

  // معلومات المستخدم الكاملة
  final String? userId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final CustomerAddress? customerAddress;
  final PaymentInfo? paymentInfo;
  final ShippingInfo? shippingInfo;

  const Order({
    required this.id,
    required this.date,
    required this.status,
    required this.total,
    required this.items,
    this.shippingAddress,
    this.trackingNumber,
    this.estimatedDelivery,
    this.notes,
    this.userId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    this.paymentInfo,
    this.shippingInfo,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => OrderStatus.processing,
      ),
      total: (json['total'] ?? 0).toDouble(),
      items:
          (json['items'] as List<dynamic>?)
              ?.map((item) => OrderItem.fromJson(item))
              .toList() ??
          [],
      shippingAddress: json['shippingAddress'],
      trackingNumber: json['trackingNumber'],
      estimatedDelivery: json['estimatedDelivery'] != null
          ? DateTime.parse(json['estimatedDelivery'])
          : null,
      notes: json['notes'],
      userId: json['userId'],
      customerName: json['customerName'],
      customerEmail: json['customerEmail'],
      customerPhone: json['customerPhone'],
      customerAddress: json['customerAddress'] != null
          ? CustomerAddress.fromJson(json['customerAddress'])
          : null,
      paymentInfo: json['paymentInfo'] != null
          ? PaymentInfo.fromJson(json['paymentInfo'])
          : null,
      shippingInfo: json['shippingInfo'] != null
          ? ShippingInfo.fromJson(json['shippingInfo'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'status': status.toString().split('.').last,
      'total': total,
      'items': items.map((item) => item.toJson()).toList(),
      'shippingAddress': shippingAddress,
      'trackingNumber': trackingNumber,
      'estimatedDelivery': estimatedDelivery?.toIso8601String(),
      'notes': notes,
      'userId': userId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'customerPhone': customerPhone,
      'customerAddress': customerAddress?.toJson(),
      'paymentInfo': paymentInfo?.toJson(),
      'shippingInfo': shippingInfo?.toJson(),
    };
  }

  // نسخة محدثة من الطلب
  Order copyWith({
    String? id,
    DateTime? date,
    OrderStatus? status,
    double? total,
    List<OrderItem>? items,
    String? shippingAddress,
    String? trackingNumber,
    DateTime? estimatedDelivery,
    String? notes,
    String? userId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    CustomerAddress? customerAddress,
    PaymentInfo? paymentInfo,
    ShippingInfo? shippingInfo,
  }) {
    return Order(
      id: id ?? this.id,
      date: date ?? this.date,
      status: status ?? this.status,
      total: total ?? this.total,
      items: items ?? this.items,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      notes: notes ?? this.notes,
      userId: userId ?? this.userId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      paymentInfo: paymentInfo ?? this.paymentInfo,
      shippingInfo: shippingInfo ?? this.shippingInfo,
    );
  }

  // حساب عدد العناصر
  int get itemCount => items.fold(0, (sum, item) => sum + item.quantity);

  // التحقق من إمكانية الإلغاء
  bool get canCancel => status == OrderStatus.processing;

  // التحقق من إمكانية التتبع
  bool get canTrack =>
      trackingNumber != null &&
      (status == OrderStatus.shipped || status == OrderStatus.delivered);

  // التحقق من إمكانية إعادة الطلب
  bool get canReorder => status == OrderStatus.delivered;
}

class OrderItem {
  final String productId;
  final String productName;
  final String imageUrl;
  final int quantity;
  final double price;
  final String? selectedColor;
  final String? selectedSize;
  final Map<String, dynamic>? customizations;

  const OrderItem({
    required this.productId,
    required this.productName,
    required this.imageUrl,
    required this.quantity,
    required this.price,
    this.selectedColor,
    this.selectedSize,
    this.customizations,
  });

  // دالة مساعدة لتنظيف رابط الصورة
  static String _cleanImageUrl(String originalUrl) {
    // إذا كان الرابط طويل جداً (أكثر من 200 حرف)، استخدم صورة افتراضية
    if (originalUrl.length > 200) {
      return 'assets/images/placeholder_product.jpg';
    }
    return originalUrl;
  }

  // إنشاء OrderItem مع تنظيف رابط الصورة
  factory OrderItem.fromProduct({
    required String productId,
    required String productName,
    required String originalImageUrl,
    required int quantity,
    required double price,
    String? selectedColor,
    String? selectedSize,
    Map<String, dynamic>? customizations,
  }) {
    return OrderItem(
      productId: productId,
      productName: productName,
      imageUrl: _cleanImageUrl(originalImageUrl),
      quantity: quantity,
      price: price,
      selectedColor: selectedColor,
      selectedSize: selectedSize,
      customizations: customizations,
    );
  }

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['productId'] ?? '',
      productName: json['productName'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      quantity: json['quantity'] ?? 1,
      price: (json['price'] ?? 0).toDouble(),
      selectedColor: json['selectedColor'],
      selectedSize: json['selectedSize'],
      customizations: json['customizations'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'price': price,
      'selectedColor': selectedColor,
      'selectedSize': selectedSize,
      'customizations': customizations,
    };
  }

  // المجموع الفرعي للعنصر
  double get subtotal => price * quantity;

  // نسخة محدثة من عنصر الطلب
  OrderItem copyWith({
    String? productId,
    String? productName,
    String? imageUrl,
    int? quantity,
    double? price,
    String? selectedColor,
    String? selectedSize,
    Map<String, dynamic>? customizations,
  }) {
    return OrderItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      selectedColor: selectedColor ?? this.selectedColor,
      selectedSize: selectedSize ?? this.selectedSize,
      customizations: customizations ?? this.customizations,
    );
  }
}

enum OrderStatus {
  processing, // قيد المعالجة
  confirmed, // مؤكد
  preparing, // قيد التحضير
  shipped, // تم الشحن
  delivered, // تم التسليم
  cancelled, // ملغي
  returned, // مرتجع
}

enum PaymentMethod {
  cash, // الدفع عند الاستلام
  card, // بطاقة ائتمان
  wallet, // محفظة إلكترونية
  bankTransfer, // تحويل بنكي
}

enum ShippingMethod {
  standard, // شحن عادي
  express, // شحن سريع
  overnight, // شحن خلال الليل
  pickup, // استلام من المتجر
}

// معلومات الشحن
class ShippingInfo {
  final String address;
  final String city;
  final String country;
  final String postalCode;
  final String? phone;
  final String? notes;
  final ShippingMethod method;
  final double cost;
  final DateTime? estimatedDelivery;

  const ShippingInfo({
    required this.address,
    required this.city,
    required this.country,
    required this.postalCode,
    this.phone,
    this.notes,
    required this.method,
    required this.cost,
    this.estimatedDelivery,
  });

  factory ShippingInfo.fromJson(Map<String, dynamic> json) {
    return ShippingInfo(
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      postalCode: json['postalCode'] ?? '',
      phone: json['phone'],
      notes: json['notes'],
      method: ShippingMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => ShippingMethod.standard,
      ),
      cost: (json['cost'] ?? 0).toDouble(),
      estimatedDelivery: json['estimatedDelivery'] != null
          ? DateTime.parse(json['estimatedDelivery'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'phone': phone,
      'notes': notes,
      'method': method.toString().split('.').last,
      'cost': cost,
      'estimatedDelivery': estimatedDelivery?.toIso8601String(),
    };
  }

  // العنوان الكامل
  String get fullAddress => '$address, $city, $country $postalCode';
}

// معلومات الدفع
class PaymentInfo {
  final PaymentMethod method;
  final PaymentStatus status;
  final double amount;
  final String? transactionId;
  final DateTime? paidAt;
  final String? cardLast4;
  final String? cardBrand;

  const PaymentInfo({
    required this.method,
    required this.status,
    required this.amount,
    this.transactionId,
    this.paidAt,
    this.cardLast4,
    this.cardBrand,
  });

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString().split('.').last == json['method'],
        orElse: () => PaymentMethod.cash,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      amount: (json['amount'] ?? 0).toDouble(),
      transactionId: json['transactionId'],
      paidAt: json['paidAt'] != null ? DateTime.parse(json['paidAt']) : null,
      cardLast4: json['cardLast4'],
      cardBrand: json['cardBrand'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method.toString().split('.').last,
      'status': status.toString().split('.').last,
      'amount': amount,
      'transactionId': transactionId,
      'paidAt': paidAt?.toIso8601String(),
      'cardLast4': cardLast4,
      'cardBrand': cardBrand,
    };
  }
}

enum PaymentStatus {
  pending, // في الانتظار
  paid, // مدفوع
  failed, // فشل
  refunded, // مسترد
}

// معلومات عنوان العميل
class CustomerAddress {
  final String fullName;
  final String phone;
  final String address;
  final String city;
  final String area;
  final String? landmark;
  final String? notes;

  const CustomerAddress({
    required this.fullName,
    required this.phone,
    required this.address,
    required this.city,
    required this.area,
    this.landmark,
    this.notes,
  });

  factory CustomerAddress.fromJson(Map<String, dynamic> json) {
    return CustomerAddress(
      fullName: json['fullName'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      area: json['area'] ?? '',
      landmark: json['landmark'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'phone': phone,
      'address': address,
      'city': city,
      'area': area,
      'landmark': landmark,
      'notes': notes,
    };
  }

  // العنوان الكامل للعرض
  String get fullAddress {
    String addr = '$address, $area, $city';
    if (landmark != null && landmark!.isNotEmpty) {
      addr += ' - $landmark';
    }
    return addr;
  }

  // معلومات الاتصال
  String get contactInfo => '$fullName - $phone';
}
