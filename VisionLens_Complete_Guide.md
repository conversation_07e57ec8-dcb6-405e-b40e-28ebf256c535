# كتيب VisionLens App - دليل شامل ومفصل

## 📋 فهرس المحتويات

1. [نظرة عامة على التطبيق](#نظرة-عامة)
2. [هيكل المشروع والملفات](#هيكل-المشروع)
3. [الشاشات والواجهات](#الشاشات-والواجهات)
4. [نظام المصادقة والمستخدمين](#نظام-المصادقة)
5. [إدارة المنتجات والفئات](#إدارة-المنتجات)
6. [نظام التسوق والطلبات](#نظام-التسوق)
7. [التكامل مع Firebase](#التكامل-مع-firebase)
8. [نماذج البيانات](#نماذج-البيانات)
9. [الخدمات والـ Services](#الخدمات)
10. [التصميم والثيم](#التصميم-والثيم)
11. [الأمان وقواعد Firebase](#الأمان)
12. [دعم المنصات المتعددة](#دعم-المنصات)
13. [النشر والتطوير](#النشر-والتطوير)
14. [الميزات المتقدمة](#الميزات-المتقدمة)
15. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 نظرة عامة على التطبيق {#نظرة-عامة}

### معلومات أساسية
- **اسم التطبيق:** VisionLens
- **النوع:** متجر إلكتروني للنظارات والعدسات اللاصقة
- **التقنية:** Flutter + Firebase
- **اللغة:** العربية (RTL Support)
- **المنصات:** Android, iOS, Web, macOS
- **معرف المشروع:** visionlens-app-5ab70

### الوصف التفصيلي
VisionLens هو تطبيق متجر إلكتروني متخصص في بيع النظارات الطبية والشمسية والعدسات اللاصقة وإكسسواراتها. يوفر تجربة تسوق سلسة مع دعم كامل للغة العربية وتكامل قوي مع خدمات Firebase.

### الأهداف الرئيسية
- توفير منصة تسوق آمنة وموثوقة للنظارات والعدسات
- تقديم تجربة مستخدم متميزة ومريحة
- دعم العملاء في اختيار المنتجات المناسبة لاحتياجاتهم
- إدارة فعالة للمخزون والطلبات
- توفير نظام إداري شامل لإدارة المتجر

### الميزات الرئيسية
- 🛍️ تسوق آمن ومريح مع خيارات دفع متنوعة
- 👓 تشكيلة واسعة من النظارات والعدسات
- 🔍 بحث وفلترة متقدمة للمنتجات
- 📱 تصميم متجاوب لجميع الأجهزة والشاشات
- 🔐 نظام مصادقة آمن مع دعم Google Sign-In
- 📊 لوحة تحكم إدارية شاملة ومتطورة
- 🌐 دعم متعدد المنصات للوصول الأوسع
- ⭐ نظام تقييمات ومراجعات للمنتجات
- 👤 إدارة الملف الشخصي والطلبات
- 🔔 نظام إشعارات ذكي

---

## 🏗️ هيكل المشروع والملفات {#هيكل-المشروع}

### البنية العامة للمشروع
```
visionlensapp/
│
├── android/                    # ملفات Android
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── java/
│   │   │   ├── kotlin/
│   │   │   └── res/
│   │   └── google-services.json
│   └── build.gradle.kts
│
├── ios/                        # ملفات iOS
│   ├── Runner/
│   │   ├── Info.plist
│   │   └── Assets.xcassets/
│   └── Runner.xcodeproj/
│
├── web/                        # ملفات Web
│   ├── index.html
│   ├── manifest.json
│   └── icons/
│
├── linux/                      # دعم Linux
├── macos/                      # دعم macOS
├── windows/                    # دعم Windows
│
├── assets/                     # موارد التطبيق
│   ├── images/
│   ├── icons/
│   └── fonts/
│
├── lib/                        # كود التطبيق الرئيسي
│   ├── models/                 # نماذج البيانات
│   ├── screens/                # الشاشات
│   ├── services/               # الخدمات
│   ├── api_service.dart
│   ├── api_service_mock.dart
│   ├── app_properties.dart
│   ├── main.dart
│   └── firebase_options.dart
│
├── firebase.json               # إعدادات Firebase
├── firestore.rules            # قواعد Firestore
├── storage.rules              # قواعد Storage
├── pubspec.yaml               # تبعيات Flutter
└── README.md
```

### شرح المجلدات الرئيسية

#### مجلد `/lib` - كود التطبيق
- **models/**: نماذج البيانات (Product, User, Order, Category, Review, etc.)
- **screens/**: جميع شاشات التطبيق منظمة حسب الوظيفة
- **services/**: خدمات Firebase والبيانات والمصادقة
- **main.dart**: نقطة البداية وتهيئة Firebase
- **app_properties.dart**: الألوان والثيمات والثوابت

#### مجلد `/assets` - الموارد
- **images/**: صور التطبيق والمنتجات والشعارات
- **icons/**: أيقونات مخصصة للتطبيق
- **fonts/**: خطوط عربية (Cairo font family)

#### إعدادات Firebase
- **firebase.json**: إعدادات المشروع والاستضافة
- **firestore.rules**: قواعد أمان قاعدة البيانات
- **storage.rules**: قواعد أمان التخزين السحابي

### مجلد الشاشات `/lib/screens`
```
screens/
├── auth/                       # شاشات المصادقة
│   ├── login_page.dart
│   ├── register_page.dart
│   ├── forgot_password_page.dart
│   └── welcome_back_page.dart
│
├── main/                       # الشاشات الرئيسية
│   ├── main_page.dart
│   ├── home_page.dart
│   └── components/
│
├── product/                    # شاشات المنتجات
│   ├── product_page.dart
│   ├── all_products_page.dart
│   ├── reviews_page.dart
│   └── components/
│
├── category/                   # شاشات الفئات
│   ├── category_list_page.dart
│   ├── category_products_page.dart
│   └── components/
│
├── shop/                       # شاشات التسوق
│   ├── cart_page.dart
│   ├── checkout_page.dart
│   ├── direct_purchase_page.dart
│   ├── filters_page.dart
│   └── components/
│
├── settings/                   # شاشات الإعدادات
│   ├── profile_page.dart
│   ├── edit_profile_page.dart
│   ├── order_history_page.dart
│   ├── address_book_page.dart
│   ├── payment_methods_page.dart
│   ├── notifications_page.dart
│   ├── prescription_page.dart
│   └── wishlist_page.dart
│
├── admin/                      # شاشات الإدارة
│   ├── admin_dashboard.dart
│   ├── products_management_page.dart
│   ├── add_product_page.dart
│   ├── edit_product_page.dart
│   ├── categories_management_page.dart
│   ├── brands_management_page.dart
│   ├── orders_management_page.dart
│   ├── users_management_page.dart
│   └── admin_accounts_page.dart
│
├── search/                     # شاشات البحث
│   └── search_page.dart
│
├── orders/                     # شاشات الطلبات
│   └── order_tracking_page.dart
│
└── splash_page.dart           # شاشة البداية
```

---

## 📱 الشاشات والواجهات {#الشاشات-والواجهات}

### 1. شاشة البداية (Splash Screen)
**الملف:** `lib/screens/splash_page.dart`

**الوظائف:**
- عرض شعار التطبيق مع أنيميشن جذاب (fade + scale)
- تحميل إعدادات التطبيق الأساسية
- التحقق من حالة تسجيل الدخول عبر SharedPreferences
- توجيه المستخدم للشاشة المناسبة (الرئيسية أو الترحيب)
- عرض معلومات الإصدار وحقوق الطبع

**المكونات:**
- أنيميشن متقدم للشعار مع تأثيرات بصرية
- مؤشر تحميل دائري
- رسائل حالة التحميل
- تدرج لوني جذاب بألوان العلامة التجارية

### 2. شاشة الترحيب (Welcome Screen)
**الملف:** `lib/screens/auth/welcome_back_page.dart`

**الوظائف:**
- ترحيب بالمستخدمين الجدد بتصميم أنيق
- عرض شعار التطبيق ووصف مختصر
- ثلاثة خيارات رئيسية: تسجيل الدخول، إنشاء حساب، المتابعة كضيف
- تصميم متجاوب يتكيف مع أحجام الشاشات المختلفة

**التصميم:**
- شعار دائري مع ظلال جذابة
- أزرار بتصميم Material Design
- ألوان متناسقة مع هوية التطبيق
- تخطيط مركزي مريح للعين

### 3. الشاشة الرئيسية (Main Navigation)
**الملف:** `lib/screens/main/main_page.dart`

**التبويبات الخمسة:**
1. **🏠 الرئيسية** - `HomePage`: عرض المحتوى الأساسي والعروض
2. **📂 الفئات** - `CategoryListPage`: تصفح المنتجات حسب النوع
3. **🔍 البحث** - `SearchPage`: البحث المتقدم عن المنتجات
4. **🛒 السلة** - `CartPage`: إدارة المشتريات والدفع
5. **👤 الملف الشخصي** - `ProfilePage`: الإعدادات والمعلومات الشخصية

**الميزات:**
- شريط تنقل سفلي مع أيقونات واضحة
- إدارة الحالة بين التبويبات باستخدام IndexedStack
- منع الخروج المباشر من التطبيق (WillPopScope)
- تأثيرات بصرية عند التنقل بين التبويبات

### 4. الصفحة الرئيسية (Home Page)
**الملف:** `lib/screens/main/home_page.dart`

**المحتويات:**
- **شريط التطبيق المخصص**: شعار + رسالة ترحيب شخصية
- **البانر الترويجي**: سلايدر تلقائي للمنتجات المميزة مع Swiper
- **الفئات الرئيسية**: عرض أفقي للفئات مع أيقونات ملونة
- **المنتجات المميزة**: قائمة أفقية قابلة للتمرير
- **المنتجات الجديدة**: أحدث الإضافات للمتجر
- **العروض الخاصة**: خصومات وعروض محدودة الوقت

**تحميل البيانات:**
- جلب البيانات من Firestore أولاً
- في حالة الفشل، استخدام البيانات المحلية المحاكاة
- RefreshIndicator لإعادة تحميل البيانات
- مؤشرات تحميل أثناء جلب البيانات

### 5. شاشات المصادقة

#### تسجيل الدخول
**الملف:** `lib/screens/auth/login_page.dart`
- نموذج البريد الإلكتروني وكلمة المرور مع التحقق من الصحة
- زر تسجيل الدخول بـ Google مع أيقونة مميزة
- رابط استعادة كلمة المرور المنسية
- التحقق من صحة البيانات قبل الإرسال
- رسائل خطأ واضحة ومفيدة

#### إنشاء حساب جديد
**الملف:** `lib/screens/auth/register_page.dart`
- نموذج شامل: الاسم الأول، الأخير، البريد، الهاتف، كلمة المرور
- التحقق من قوة كلمة المرور مع مؤشر بصري
- تأكيد كلمة المرور مع التحقق من التطابق
- إرسال رمز التحقق للبريد الإلكتروني
- ربط تلقائي مع Firebase Auth

#### استعادة كلمة المرور
**الملف:** `lib/screens/auth/forgot_password_page.dart`
- حقل إدخال البريد الإلكتروني مع التحقق من الصحة
- إرسال رابط الاستعادة عبر Firebase Auth
- رسائل تأكيد واضحة للمستخدم
- إرشادات للخطوات التالية

### 6. شاشات المنتجات

#### صفحة المنتج
**الملف:** `lib/screens/product/product_page.dart`
- معرض صور تفاعلي مع إمكانية التكبير والتصغير
- معلومات شاملة: الاسم، السعر، الوصف، التقييم
- عرض المواصفات التقنية في جدول منظم
- خيارات المنتج: الألوان، الأحجام، قوة العدسة
- أزرار إضافة للسلة والمفضلة مع تأثيرات بصرية
- قسم المراجعات والتقييمات من المستخدمين

#### جميع المنتجات
**الملف:** `lib/screens/product/all_products_page.dart`
- عرض شبكي متجاوب للمنتجات (2 أعمدة على الهاتف، أكثر على التابلت)
- فلترة متقدمة: الفئة، السعر، العلامة التجارية، التقييم، التوفر
- خيارات ترتيب: الأحدث، الأقل سعراً، الأعلى سعراً، الأعلى تقييماً
- بحث سريع مع اقتراحات تلقائية
- تحميل تدريجي للمنتجات (Pagination)

#### المراجعات
**الملف:** `lib/screens/product/reviews_page.dart`
- عرض جميع مراجعات منتج معين مع التقييمات
- إحصائيات التقييمات مع رسم بياني بالنجوم
- فلترة المراجعات حسب عدد النجوم
- إمكانية إضافة مراجعة جديدة للمشترين فقط
- نظام الإعجاب والتعليق على المراجعات

### 7. شاشات الفئات

#### قائمة الفئات
**الملف:** `lib/screens/category/category_list_page.dart`
- عرض شبكي لجميع فئات المنتجات
- صور تمثيلية عالية الجودة لكل فئة
- عدد المنتجات المتاحة في كل فئة
- بحث سريع في الفئات
- ترتيب الفئات حسب الشعبية أو الأبجدية

#### منتجات الفئة
**الملف:** `lib/screens/category/category_products_page.dart`
- عرض جميع المنتجات التابعة لفئة محددة
- فلترة خاصة بكل فئة (مثل شكل الإطار للنظارات)
- إحصائيات الفئة: عدد المنتجات، متوسط السعر
- اقتراحات منتجات مشابهة من فئات أخرى

### 8. شاشات التسوق

#### السلة
**الملف:** `lib/screens/shop/cart_page.dart`
- قائمة تفصيلية بجميع المنتجات المختارة
- صورة وتفاصيل كل منتج مع الخيارات المحددة
- تعديل الكمية مع أزرار + و - تفاعلية
- حذف المنتجات من السلة مع تأكيد
- حساب تلقائي: المجموع الفرعي، الشحن، الضرائب، الإجمالي
- إدخال كوبونات الخصم مع التحقق من الصحة
- زر واضح ومميز للانتقال لعملية الدفع

#### الدفع
**الملف:** `lib/screens/shop/checkout_page.dart`
- اختيار عنوان التوصيل من القائمة المحفوظة أو إضافة جديد
- اختيار طريقة الدفع: نقداً عند التوصيل، بطاقة ائتمان، محفظة رقمية
- مراجعة نهائية شاملة للطلب مع جميع التفاصيل
- حقل ملاحظات إضافية للطلب
- تأكيد الطلب مع إرسال إشعار فوري
- انتقال لصفحة تأكيد الطلب مع رقم التتبع

#### الشراء المباشر
**الملف:** `lib/screens/shop/direct_purchase_page.dart`
- شراء فوري لمنتج واحد دون المرور بالسلة
- مفيد للمشتريات العاجلة والسريعة
- نفس خطوات الدفع العادية لكن مبسطة
- خيار حفظ المنتج للسلة بدلاً من الشراء المباشر

### 9. شاشات الملف الشخصي

#### الملف الشخصي الرئيسي
**الملف:** `lib/screens/settings/profile_page.dart`
- عرض معلومات المستخدم مع صورته الشخصية
- إحصائيات شخصية: عدد الطلبات، المنتجات المفضلة، النقاط المكتسبة
- قائمة منظمة بروابط جميع الإعدادات والخدمات
- خيارات الحساب: تعديل الملف، الأمان، الخصوصية
- زر تسجيل الخروج مع تأكيد الإجراء

#### تعديل الملف الشخصي
**الملف:** `lib/screens/settings/edit_profile_page.dart`
- نموذج لتحديث المعلومات: الاسم، البريد، الهاتف، تاريخ الميلاد
- تغيير الصورة الشخصية: رفع من المعرض أو التقاط بالكاميرا
- التحقق من صحة البيانات قبل الحفظ
- معاينة التغييرات قبل التأكيد
- حفظ التغييرات في Firebase وتحديث الواجهة

#### سجل الطلبات
**الملف:** `lib/screens/settings/order_history_page.dart`
- قائمة شاملة بجميع الطلبات مرتبة حسب التاريخ
- كل طلب يعرض: الرقم، التاريخ، الحالة، الإجمالي
- فلترة الطلبات حسب الحالة أو التاريخ
- عرض تفاصيل كل طلب مع المنتجات المطلوبة
- تتبع حالة التوصيل مع خريطة تفاعلية
- خيار إعادة طلب نفس المنتجات بنقرة واحدة

#### دفتر العناوين
**الملف:** `lib/screens/settings/address_book_page.dart`
- عرض جميع العناوين المحفوظة مع تفاصيلها
- إضافة عناوين جديدة مع تحديد الموقع على الخريطة
- تعديل العناوين الموجودة أو حذفها
- تحديد العنوان الافتراضي للطلبات
- تصنيف العناوين: منزل، عمل، أخرى
- التحقق من صحة العنوان مع خدمات الخرائط

#### طرق الدفع
**الملف:** `lib/screens/settings/payment_methods_page.dart`
- عرض البطاقات والطرق المالية المحفوظة
- إضافة بطاقات جديدة مع تشفير آمن للمعلومات
- تحديد الطريقة الافتراضية للدفع
- حذف الطرق غير المستخدمة مع تأكيد
- دعم أنواع مختلفة: فيزا، ماستركارد، محافظ رقمية
- التحقق من صحة بيانات البطاقة قبل الحفظ

#### المفضلة
**الملف:** `lib/screens/settings/wishlist_page.dart`
- عرض شبكي لجميع المنتجات المفضلة
- إضافة المنتجات للسلة مباشرة من المفضلة
- إزالة المنتجات من المفضلة مع تأكيد
- فلترة وترتيب المفضلة حسب التاريخ أو السعر
- مشاركة قائمة المفضلة مع الأصدقاء
- إشعارات عند توفر خصومات على المنتجات المفضلة

#### الوصفة الطبية
**الملف:** `lib/screens/settings/prescription_page.dart`
- حفظ وإدارة الوصفات الطبية للنظارات
- رفع صور الوصفات أو إدخال القيم يدوياً
- حفظ تاريخ الوصفات مع تذكير بمواعيد التجديد
- ربط الوصفات بالطلبات المناسبة
- مشاركة الوصفة مع طبيب العيون
- أرشيف للوصفات القديمة مع إمكانية المقارنة

### 10. شاشات الإدارة

#### لوحة التحكم الرئيسية
**الملف:** `lib/screens/admin/admin_dashboard.dart`
- إحصائيات سريعة: المنتجات، الفئات، الطلبات، المستخدمين
- بطاقات تفاعلية لكل إحصائية مع ألوان مميزة
- رسوم بيانية للمبيعات والأرباح
- شبكة أدوات الإدارة للوصول السريع
- قائمة الطلبات الأخيرة مع إمكانية المعالجة السريعة
- تقارير الأداء اليومية والشهرية

#### إدارة المنتجات
**الملف:** `lib/screens/admin/products_management_page.dart`
- جدول شامل لجميع المنتجات مع معلوماتها الأساسية
- أدوات بحث وفلترة متقدمة للمنتجات
- أزرار سريعة: إضافة، تعديل، حذف، تكرار
- إحصائيات المخزون والمبيعات لكل منتج
- تصدير قوائم المنتجات بصيغ مختلفة
- إدارة جماعية للمنتجات (تحديث الأسعار، الخصومات)

#### إضافة منتج جديد
**الملف:** `lib/screens/admin/add_product_page.dart`
- نموذج شامل: الاسم، الوصف، السعر، الفئة، العلامة التجارية
- رفع صور متعددة للمنتج مع إمكانية الترتيب
- تحديد المواصفات التقنية والخيارات المتاحة
- إعدادات المخزون: الكمية، الحد الأدنى، التنبيهات
- معاينة المنتج قبل الحفظ
- حفظ كمسودة أو نشر مباشرة

#### إدارة الفئات
**الملف:** `lib/screens/admin/categories_management_page.dart`
- عرض هيكلي للفئات الرئيسية والفرعية
- إضافة فئات جديدة مع تحديد المستوى
- تعديل الفئات الموجودة أو حذفها
- رفع صور تمثيلية لكل فئة
- ترتيب الفئات حسب الأولوية في العرض
- إحصائيات عدد المنتجات في كل فئة

#### إدارة الطلبات
**الملف:** `lib/screens/admin/orders_management_page.dart`
- عرض جميع الطلبات مع فلترة حسب الحالة
- تفاصيل كل طلب: العميل، المنتجات، العنوان، الدفع
- تحديث حالة الطلبات مع إشعارات تلقائية للعملاء
- طباعة فواتير وملصقات الشحن
- إحصائيات المبيعات والأرباح
- تقارير مفصلة عن الأداء

#### إدارة المستخدمين
**الملف:** `lib/screens/admin/users_management_page.dart`
- قائمة شاملة بجميع المستخدمين مع معلوماتهم
- إحصائيات نشاط كل مستخدم: الطلبات، المفضلة، التقييمات
- تعديل صلاحيات المستخدمين أو ترقيتهم لمدراء
- تعطيل الحسابات المخالفة مع تسجيل الأسباب
- إرسال إشعارات جماعية أو فردية
- تصدير قوائم المستخدمين للتسويق

---

## 🔐 نظام المصادقة والمستخدمين {#نظام-المصادقة}

### آلية المصادقة
يعتمد التطبيق على نظام مصادقة متطور ومتعدد الطبقات يضمن الأمان والسهولة. النظام يدعم عدة طرق لتسجيل الدخول ويتكامل بشكل كامل مع Firebase Authentication.

### طرق تسجيل الدخول

#### تسجيل الدخول بالبريد الإلكتروني
- التحقق من صحة البيانات محلياً قبل الإرسال
- تشفير كلمة المرور قبل الإرسال لـ Firebase
- حفظ بيانات المستخدم محلياً لتسريع الدخول
- إدارة جلسات المستخدم مع انتهاء صلاحية تلقائي
- رسائل خطأ واضحة ومفيدة للمستخدم

#### تسجيل الدخول بـ Google
- استخدام Google Sign-In SDK الرسمي
- فتح نافذة مصادقة Google الآمنة
- استيراد المعلومات الأساسية من حساب Google
- إنشاء حساب Firebase تلقائياً مع ربط الحسابين
- دعم تسجيل الدخول السريع في الزيارات التالية

### أنواع المستخدمين

#### المستخدم العادي (Customer)
- النوع الافتراضي لجميع المستخدمين الجدد
- تصفح المنتجات وإضافتها للسلة والمفضلة
- إتمام عمليات الشراء وتتبع الطلبات
- إدارة الملف الشخصي والعناوين وطرق الدفع
- كتابة مراجعات وتقييمات للمنتجات المشتراة
- الوصول لسجل الطلبات والفواتير

#### المدير (Admin)
- جميع صلاحيات المستخدم العادي
- إدارة شاملة للمنتجات: إضافة، تعديل، حذف
- إدارة الفئات والعلامات التجارية
- معالجة ومتابعة جميع الطلبات
- إدارة المستخدمين وصلاحياتهم
- الوصول للإحصائيات والتقارير المالية
- إدارة المحتوى والعروض الترويجية

#### الضيف (Guest)
- تصفح محدود للمنتجات والفئات
- لا يمكن إضافة منتجات للسلة أو الشراء
- لا يمكن حفظ المفضلة أو كتابة المراجعات
- إمكانية التسجيل في أي وقت لتفعيل جميع الميزات

### إدارة الجلسات والأمان
- حفظ حالة تسجيل الدخول في SharedPreferences
- تشفير البيانات الحساسة قبل التخزين المحلي
- انتهاء صلاحية الجلسة تلقائياً بعد فترة عدم نشاط
- التحقق من صحة الجلسة عند كل عملية حساسة
- إمكانية تسجيل الخروج من جميع الأجهزة

---

## 🛍️ إدارة المنتجات والفئات {#إدارة-المنتجات}

### نموذج المنتج
**الملف:** `lib/models/product.dart`

#### المعلومات الأساسية
- **المعرف الفريد**: ID تلقائي لكل منتج
- **الاسم والوصف**: نصوص باللغة العربية مع دعم HTML
- **السعر**: السعر الحالي والسعر الأصلي (قبل الخصم)
- **الصور**: صورة رئيسية ومعرض صور إضافية
- **الفئة**: ربط بفئة رئيسية وفئات فرعية
- **التقييم**: متوسط التقييمات وعدد المراجعات
- **المخزون**: الكمية المتاحة وحالة التوفر

#### خصائص خاصة بالنظارات والعدسات
- **العلامة التجارية**: الشركة المصنعة
- **نوع المنتج**: نظارات طبية، شمسية، عدسات لاصقة، إلخ
- **المادة**: مادة الإطار أو العدسة
- **اللون**: الألوان المتاحة مع صور لكل لون
- **الحجم**: الأحجام المختلفة مع المقاسات
- **قوة العدسة**: للعدسات اللاصقة والطبية
- **الحاجة لوصفة طبية**: منتجات تتطلب وصفة

#### المواصفات التقنية
- **أبعاد الإطار**: العرض، الارتفاع، طول الذراع
- **مادة الصنع**: بلاستيك، معدن، تيتانيوم، إلخ
- **نوع العدسة**: مضادة للانعكاس، مرشحة للضوء الأزرق
- **الحماية**: مستوى الحماية من الأشعة فوق البنفسجية
- **الوزن**: وزن المنتج بالجرام
- **بلد المنشأ**: البلد المصنع للمنتج
- **الضمان**: فترة الضمان وشروطه

### أنواع المنتجات

#### النظارات الطبية
- نظارات لتصحيح النظر (قصر وطول النظر)
- نظارات للاستجماتيزم
- نظارات متعددة البؤر (Progressive)
- نظارات للحماية من الضوء الأزرق
- إطارات بدون عدسات (للتركيب لاحقاً)

#### النظارات الشمسية
- نظارات شمسية عادية
- نظارات شمسية طبية (بوصفة)
- نظارات رياضية مقاومة للكسر
- نظارات مستقطبة لتقليل الوهج
- نظارات بعدسات متغيرة اللون

#### العدسات اللاصقة
- عدسات يومية (Daily)
- عدسات أسبوعية وشهرية
- عدسات ملونة تجميلية
- عدسات طبية لتصحيح النظر
- عدسات للاستجماتيزم (Toric)
- عدسات متعددة البؤر

#### نظارات القراءة
- نظارات قراءة بدرجات مختلفة
- نظارات للكمبيوتر والشاشات
- نظارات مكبرة للأعمال الدقيقة
- نظارات قابلة للطي والحمل

#### الإكسسوارات
- حافظات وعلب النظارات
- سلاسل وحبال النظارات
- أدوات تنظيف ومناديل خاصة
- قطع غيار: مسامير، أنف، أذرع
- حوامل ومنظمات النظارات

#### محاليل العدسات
- محاليل تنظيف وتطهير
- محاليل حفظ العدسات
- قطرات مرطبة للعيون
- أدوات إدخال وإخراج العدسات

### إدارة المنتجات في النظام

#### إضافة منتج جديد
- نموذج شامل لجميع معلومات المنتج
- رفع صور عالية الجودة مع ضغط تلقائي
- تحديد الفئة والعلامة التجارية
- إدخال المواصفات والخيارات المتاحة
- تحديد السعر والخصومات
- إعدادات المخزون والتنبيهات
- معاينة المنتج قبل النشر

#### تعديل المنتجات
- تحديث أي معلومة في المنتج
- إضافة أو حذف صور
- تعديل الأسعار والخصومات
- تحديث المخزون والتوفر
- تغيير الفئة أو العلامة التجارية
- حفظ تاريخ التعديلات

#### إدارة المخزون
- تتبع الكميات المتاحة لكل منتج
- تنبيهات عند انخفاض المخزون
- تحديث تلقائي عند البيع أو الإرجاع
- تقارير المخزون الشهرية
- إدارة المخزون حسب الخيارات (لون، حجم)

### إدارة الفئات

#### الفئات الرئيسية
- تنظيم هيكلي للمنتجات
- صور تمثيلية جذابة لكل فئة
- وصف مختصر لكل فئة
- ترتيب الفئات حسب الأولوية
- إحصائيات عدد المنتجات في كل فئة

#### الفئات الفرعية
- تقسيم أكثر تفصيلاً للمنتجات
- ربط بالفئات الرئيسية
- فلترة متقدمة داخل كل فئة فرعية
- إمكانية نقل المنتجات بين الفئات

#### العلامات التجارية
- إدارة منفصلة للعلامات التجارية
- شعارات وصور العلامات
- صفحات مخصصة لكل علامة تجارية
- إحصائيات مبيعات كل علامة

---

## 🛒 نظام التسوق والطلبات {#نظام-التسوق}

### إدارة السلة

#### إضافة المنتجات للسلة
- إضافة فورية مع تأكيد بصري
- اختيار الخيارات: اللون، الحجم، القوة
- تحديد الكمية المطلوبة
- التحقق من توفر المنتج والكمية
- حفظ السلة محلياً للاستمرارية

#### إدارة محتويات السلة
- عرض تفصيلي لكل منتج في السلة
- تعديل الكمية أو الخيارات
- حذف منتجات من السلة
- حفظ للمفضلة بدلاً من الحذف
- حساب تلقائي للمجاميع والضرائب

#### حسابات السلة
- **المجموع الفرعي**: مجموع أسعار المنتجات
- **الخصومات**: كوبونات وعروض خاصة
- **الشحن**: حساب تكلفة الشحن حسب المنطقة
- **الضرائب**: ضريبة القيمة المضافة (10%)
- **الإجمالي النهائي**: المبلغ الكامل للدفع

### عملية الدفع

#### معلومات التوصيل
- اختيار عنوان من القائمة المحفوظة
- إضافة عنوان جديد مع التحقق من الصحة
- تحديد وقت التوصيل المفضل
- إضافة ملاحظات خاصة للتوصيل
- حساب تكلفة الشحن حسب المسافة

#### طرق الدفع المتاحة
- **الدفع عند التوصيل**: نقداً للمندوب
- **البطاقة الائتمانية**: فيزا، ماستركارد
- **البطاقة المدينة**: خصم مباشر من الحساب
- **التحويل البنكي**: تحويل مباشر
- **المحافظ الرقمية**: PayPal، Apple Pay، Google Pay

#### تأكيد الطلب
- مراجعة نهائية لجميع تفاصيل الطلب
- التأكد من صحة العنوان ومعلومات الاتصال
- اختيار طريقة الدفع وتأكيد البيانات
- قراءة وقبول الشروط والأحكام
- إرسال الطلب وإنشاء رقم تتبع فريد

### إدارة الطلبات

#### حالات الطلب
1. **في الانتظار**: طلب جديد لم يتم تأكيده بعد
2. **مؤكد**: تم تأكيد الطلب وبدء المعالجة
3. **قيد التجهيز**: جاري تحضير المنتجات
4. **جاهز للشحن**: تم تجهيز الطلب وجاهز للإرسال
5. **تم الشحن**: الطلب في الطريق للعميل
6. **تم التوصيل**: وصل الطلب للعميل بنجاح
7. **ملغي**: تم إلغاء الطلب لأي سبب
8. **مرتجع**: تم إرجاع الطلب من العميل

#### تتبع الطلبات
- رقم تتبع فريد لكل طلب
- تحديثات فورية عن حالة الطلب
- إشعارات SMS وبريد إلكتروني
- خريطة تفاعلية لموقع الطلب
- تقدير زمني للوصول

#### معالجة الطلبات (للمدراء)
- قائمة شاملة بجميع الطلبات
- فلترة حسب الحالة أو التاريخ
- تحديث حالة الطلبات مع إشعارات تلقائية
- طباعة فواتير وملصقات الشحن
- إدارة المرتجعات والاستبدالات

### الفواتير والمحاسبة

#### إنشاء الفواتير
- فاتورة تلقائية لكل طلب
- تفاصيل شاملة: المنتجات، الأسعار، الضرائب
- معلومات العميل والشركة
- رقم فاتورة فريد وتاريخ الإصدار
- إمكانية طباعة أو تحميل PDF

#### التقارير المالية
- تقارير المبيعات اليومية والشهرية
- إحصائيات الأرباح والخسائر
- تحليل أداء المنتجات والفئات
- تقارير الضرائب والرسوم
- مقارنات الأداء بين الفترات المختلفة

---

## 🔥 التكامل مع Firebase {#التكامل-مع-firebase}

### إعدادات المشروع
- **معرف المشروع**: visionlens-app-5ab70
- **النطاق**: visionlens-app-5ab70.firebaseapp.com
- **التخزين**: visionlens-app-5ab70.appspot.com
- **المنطقة**: us-central1 (افتراضي)

### خدمات Firebase المستخدمة

#### Firebase Authentication
- **تسجيل الدخول بالبريد الإلكتروني وكلمة المرور**
- **تسجيل الدخول بـ Google** مع Google Sign-In SDK
- **استعادة كلمة المرور** عبر البريد الإلكتروني
- **التحقق من البريد الإلكتروني** للحسابات الجديدة
- **إدارة الجلسات** مع انتهاء صلاحية تلقائي
- **حماية متقدمة** ضد الهجمات والتسجيل المزيف

#### Cloud Firestore
- **قاعدة بيانات NoSQL** مرنة وقابلة للتوسع
- **مزامنة فورية** للبيانات عبر جميع الأجهزة
- **استعلامات متقدمة** مع فهرسة تلقائية
- **دعم العمليات المعقدة** مع Transactions
- **تخزين آمن** مع قواعد أمان مخصصة

##### مجموعات البيانات الرئيسية:
- **products**: جميع المنتجات مع تفاصيلها الكاملة
- **categories**: فئات المنتجات والتصنيفات
- **users**: معلومات المستخدمين وملفاتهم الشخصية
- **orders**: الطلبات مع تفاصيل الشراء والتوصيل
- **reviews**: مراجعات وتقييمات المنتجات
- **addresses**: عناوين التوصيل للمستخدمين
- **notifications**: الإشعارات والرسائل
- **admins**: قائمة المدراء وصلاحياتهم
- **settings**: إعدادات التطبيق العامة

#### Firebase Storage
- **تخزين الصور والملفات** بجودة عالية
- **ضغط تلقائي** للصور لتوفير المساحة
- **روابط آمنة** للوصول للملفات
- **نسخ احتياطية** تلقائية للملفات المهمة

##### أنواع الملفات المخزنة:
- **صور المنتجات**: الصورة الرئيسية ومعرض الصور
- **صور الفئات**: صور تمثيلية للفئات
- **صور المستخدمين**: الصور الشخصية
- **الوصفات الطبية**: صور الوصفات المرفوعة
- **شعارات العلامات التجارية**: لوجوهات الشركات

#### Firebase Analytics
- **تتبع سلوك المستخدمين** داخل التطبيق
- **إحصائيات الاستخدام** المفصلة
- **تحليل المبيعات** والتحويلات
- **تقارير الأداء** للصفحات والميزات
- **تجميع البيانات** لاتخاذ قرارات مدروسة

#### Firebase Cloud Messaging (FCM)
- **إشعارات فورية** للطلبات والعروض
- **رسائل مخصصة** حسب اهتمامات المستخدم
- **إشعارات جماعية** للعروض والتحديثات
- **إشعارات إدارية** للمدراء عن الطلبات الجديدة

### هيكل قاعدة البيانات

#### مجموعة Products
```
products/{productId}
├── id: string
├── name: string
├── description: string
├── price: number
├── originalPrice: number (optional)
├── image: string (URL)
├── images: array of strings
├── categoryId: string
├── categoryName: string
├── brand: string
├── type: string (enum)
├── rating: number
├── reviewsCount: number
├── inStock: boolean
├── stockQuantity: number
├── specifications: object
├── availableColors: array
├── availableSizes: array
├── availablePowers: array
├── isNew: boolean
├── isFeatured: boolean
├── isOnSale: boolean
├── discountPercentage: number
├── createdAt: timestamp
└── updatedAt: timestamp
```

#### مجموعة Users
```
users/{userId}
├── id: string
├── email: string
├── firstName: string
├── lastName: string
├── phone: string
├── profileImage: string (URL)
├── isEmailVerified: boolean
├── isPhoneVerified: boolean
├── role: string (customer/admin)
├── favoriteProducts: array
├── addresses: array
├── paymentMethods: array
├── preferences: object
├── createdAt: timestamp
└── updatedAt: timestamp
```

#### مجموعة Orders
```
orders/{orderId}
├── id: string
├── userId: string
├── items: array of objects
├── subtotal: number
├── shipping: number
├── tax: number
├── total: number
├── status: string (enum)
├── shippingAddress: object
├── paymentMethod: string
├── paymentStatus: string
├── notes: string
├── trackingNumber: string
├── createdAt: timestamp
├── updatedAt: timestamp
├── shippedAt: timestamp
└── deliveredAt: timestamp
```

### قواعد الأمان

#### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // دالة للتحقق من المصادقة
    function isAuthenticated() {
      return request.auth != null;
    }

    // دالة للتحقق من الملكية
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // دالة للتحقق من صلاحيات الإدارة
    function isAdmin() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    // قواعد المنتجات - قراءة للجميع، كتابة للمدراء
    match /products/{productId} {
      allow read: if true;
      allow write, delete: if isAdmin();
    }

    // قواعد المستخدمين - كل مستخدم يدير بياناته
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin();
    }

    // قواعد الطلبات - المستخدم يرى طلباته، المدير يرى الكل
    match /orders/{orderId} {
      allow read, write: if isAuthenticated() &&
                         (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated();
    }
  }
}
```

#### Storage Security Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // دالة للتحقق من نوع الملف
    function isValidImageType() {
      return request.resource.contentType.matches('image/.*');
    }

    // دالة للتحقق من حجم الملف (أقل من 5MB)
    function isValidSize() {
      return request.resource.size < 5 * 1024 * 1024;
    }

    // صور المنتجات - قراءة للجميع، كتابة للمدراء
    match /products/{productId}/{imageId} {
      allow read: if true;
      allow write, delete: if isAdmin() && isValidImageType() && isValidSize();
    }

    // صور المستخدمين - كل مستخدم يدير صوره
    match /users/{userId}/{imageId} {
      allow read: if true;
      allow write, delete: if request.auth != null &&
                           request.auth.uid == userId &&
                           isValidImageType() && isValidSize();
    }
  }
}
```

### خدمات Firebase المخصصة

#### FirebaseRealService
**الملف**: `lib/services/firebase_real_service.dart`
- تهيئة Firebase للمنصات المختلفة
- إدارة المصادقة مع Google Sign-In
- التعامل مع أخطاء الاتصال والمهلة الزمنية
- حفظ حالة المستخدم محلياً

#### FirebaseWebService
**الملف**: `lib/services/firebase_web_service.dart`
- تهيئة خاصة للويب مع JavaScript SDK
- التعامل مع قيود المتصفح والأمان
- دعم PWA وإشعارات الويب
- تحسين الأداء للتطبيقات الويب

#### FirestoreDataService
**الملف**: `lib/services/firestore_data_service.dart`
- عمليات CRUD للمنتجات والفئات
- استعلامات معقدة مع فلترة وترتيب
- مزامنة البيانات بين المحلي والسحابي
- إدارة التخزين المؤقت للأداء

### تحسين الأداء

#### استراتيجيات التخزين المؤقت
- حفظ البيانات المتكررة محلياً
- تحديث تدريجي للبيانات المتغيرة
- ضغط البيانات قبل التخزين
- مسح التخزين المؤقت المنتهي الصلاحية

#### تحسين الاستعلامات
- استخدام الفهارس المناسبة
- تحديد حجم النتائج (Pagination)
- استعلامات مركبة بدلاً من متعددة
- تجميع البيانات ذات الصلة

#### إدارة الاتصال
- إعادة المحاولة التلقائية عند فشل الاتصال
- التبديل للوضع المحلي عند عدم توفر الإنترنت
- ضغط البيانات المرسلة والمستقبلة
- استخدام WebSocket للتحديثات الفورية

---

## 📊 نماذج البيانات {#نماذج-البيانات}

### نموذج المنتج (Product Model)
**الملف**: `lib/models/product.dart`

#### الخصائص الأساسية
- **id**: معرف فريد للمنتج
- **name**: اسم المنتج باللغة العربية
- **description**: وصف مفصل للمنتج
- **price**: السعر الحالي
- **originalPrice**: السعر الأصلي قبل الخصم
- **image**: الصورة الرئيسية للمنتج
- **images**: مجموعة صور إضافية
- **categoryId**: معرف الفئة التابع لها
- **categoryName**: اسم الفئة
- **rating**: متوسط التقييم
- **reviewsCount**: عدد المراجعات
- **inStock**: حالة التوفر
- **stockQuantity**: الكمية المتاحة

#### خصائص خاصة بالنظارات
- **brand**: العلامة التجارية
- **type**: نوع المنتج (enum)
- **material**: مادة الصنع
- **color**: اللون الأساسي
- **size**: الحجم أو المقاس
- **power**: قوة العدسة (للعدسات اللاصقة)
- **requiresPrescription**: يتطلب وصفة طبية
- **availableColors**: الألوان المتاحة
- **availableSizes**: الأحجام المتاحة
- **availablePowers**: القوى المتاحة

#### معلومات إضافية
- **specifications**: مواصفات تقنية مفصلة
- **keywords**: كلمات مفتاحية للبحث
- **createdAt**: تاريخ الإضافة
- **updatedAt**: تاريخ آخر تحديث
- **isNew**: منتج جديد
- **isFeatured**: منتج مميز
- **isOnSale**: في حالة تخفيض
- **discountPercentage**: نسبة الخصم

#### الدوال المساعدة
- **finalPrice**: السعر النهائي بعد الخصم
- **hasDiscount**: التحقق من وجود خصم
- **calculatedDiscountPercentage**: حساب نسبة الخصم
- **isAvailable**: التحقق من التوفر
- **typeDescription**: وصف نوع المنتج بالعربية

### نموذج المستخدم (User Model)
**الملف**: `lib/models/user_simple.dart`

#### المعلومات الشخصية
- **id**: معرف فريد للمستخدم
- **email**: البريد الإلكتروني
- **firstName**: الاسم الأول
- **lastName**: الاسم الأخير
- **phone**: رقم الهاتف
- **profileImage**: صورة الملف الشخصي
- **dateOfBirth**: تاريخ الميلاد
- **gender**: الجنس

#### حالة الحساب
- **isEmailVerified**: تم التحقق من البريد
- **isPhoneVerified**: تم التحقق من الهاتف
- **role**: دور المستخدم (customer/admin)
- **isActive**: الحساب نشط
- **lastLoginAt**: آخر تسجيل دخول
- **createdAt**: تاريخ إنشاء الحساب
- **updatedAt**: تاريخ آخر تحديث

#### التفضيلات والإعدادات
- **preferences**: إعدادات شخصية
- **favoriteProducts**: المنتجات المفضلة
- **notificationSettings**: إعدادات الإشعارات
- **language**: اللغة المفضلة
- **currency**: العملة المفضلة

#### الدوال المساعدة
- **fullName**: الاسم الكامل
- **displayName**: الاسم للعرض
- **isAdmin**: التحقق من صلاحيات الإدارة
- **canPurchase**: إمكانية الشراء

### نموذج الطلب (Order Model)
**الملف**: `lib/models/order.dart`

#### معلومات الطلب الأساسية
- **id**: رقم الطلب الفريد
- **userId**: معرف المستخدم صاحب الطلب
- **orderNumber**: رقم الطلب للعرض
- **items**: قائمة المنتجات المطلوبة
- **status**: حالة الطلب الحالية
- **notes**: ملاحظات إضافية

#### التفاصيل المالية
- **subtotal**: المجموع الفرعي
- **shipping**: تكلفة الشحن
- **tax**: الضرائب
- **discount**: قيمة الخصم
- **total**: الإجمالي النهائي
- **currency**: العملة المستخدمة

#### معلومات التوصيل والدفع
- **shippingAddress**: عنوان التوصيل
- **billingAddress**: عنوان الفوترة
- **paymentMethod**: طريقة الدفع
- **paymentStatus**: حالة الدفع
- **trackingNumber**: رقم التتبع

#### التواريخ المهمة
- **createdAt**: تاريخ إنشاء الطلب
- **updatedAt**: تاريخ آخر تحديث
- **confirmedAt**: تاريخ التأكيد
- **shippedAt**: تاريخ الشحن
- **deliveredAt**: تاريخ التوصيل
- **estimatedDelivery**: التوصيل المتوقع

#### حالات الطلب (OrderStatus)
- **pending**: في الانتظار
- **confirmed**: مؤكد
- **processing**: قيد التجهيز
- **shipped**: تم الشحن
- **delivered**: تم التوصيل
- **cancelled**: ملغي
- **returned**: مرتجع

### نموذج الفئة (Category Model)
**الملف**: `lib/models/category.dart`

#### المعلومات الأساسية
- **id**: معرف فريد للفئة
- **name**: اسم الفئة
- **description**: وصف الفئة
- **image**: صورة تمثيلية
- **icon**: أيقونة الفئة
- **color**: لون مميز للفئة

#### التنظيم الهيكلي
- **parentId**: معرف الفئة الأب (للفئات الفرعية)
- **level**: مستوى الفئة في الهيكل
- **order**: ترتيب العرض
- **isActive**: الفئة نشطة
- **isVisible**: مرئية للمستخدمين

#### الإحصائيات
- **productsCount**: عدد المنتجات
- **subcategoriesCount**: عدد الفئات الفرعية
- **popularityScore**: نقاط الشعبية
- **createdAt**: تاريخ الإنشاء
- **updatedAt**: تاريخ آخر تحديث

### نموذج المراجعة (Review Model)
**الملف**: `lib/models/review.dart`

#### معلومات المراجعة
- **id**: معرف فريد للمراجعة
- **productId**: معرف المنتج المراجع
- **userId**: معرف المستخدم الكاتب
- **userName**: اسم المستخدم للعرض
- **userImage**: صورة المستخدم
- **rating**: التقييم بالنجوم (1-5)
- **title**: عنوان المراجعة
- **content**: محتوى المراجعة
- **images**: صور مرفقة مع المراجعة

#### معلومات إضافية
- **isVerifiedPurchase**: مشترٍ محقق
- **helpfulCount**: عدد الإعجابات
- **reportCount**: عدد البلاغات
- **isApproved**: مراجعة معتمدة
- **createdAt**: تاريخ الكتابة
- **updatedAt**: تاريخ آخر تعديل

### نموذج العنوان (Address Model)
**الملف**: `lib/models/address.dart`

#### معلومات العنوان
- **id**: معرف فريد للعنوان
- **userId**: معرف المستخدم المالك
- **type**: نوع العنوان (منزل، عمل، أخرى)
- **title**: عنوان مختصر
- **fullName**: الاسم الكامل للمستلم
- **phone**: رقم هاتف المستلم

#### التفاصيل الجغرافية
- **country**: البلد
- **city**: المدينة
- **district**: المنطقة أو الحي
- **street**: الشارع
- **buildingNumber**: رقم المبنى
- **floor**: الطابق
- **apartment**: رقم الشقة
- **postalCode**: الرمز البريدي
- **landmark**: معلم مميز قريب

#### الإعدادات
- **isDefault**: العنوان الافتراضي
- **isActive**: العنوان نشط
- **latitude**: خط العرض
- **longitude**: خط الطول
- **createdAt**: تاريخ الإضافة
- **updatedAt**: تاريخ آخر تحديث

### نموذج طريقة الدفع (Payment Method Model)
**الملف**: `lib/models/payment_method.dart`

#### معلومات طريقة الدفع
- **id**: معرف فريد لطريقة الدفع
- **userId**: معرف المستخدم المالك
- **type**: نوع الدفع (بطاقة، محفظة، تحويل)
- **title**: عنوان مختصر
- **isDefault**: الطريقة الافتراضية
- **isActive**: الطريقة نشطة

#### تفاصيل البطاقة (مشفرة)
- **cardNumber**: رقم البطاقة (مخفي جزئياً)
- **cardHolderName**: اسم حامل البطاقة
- **expiryMonth**: شهر انتهاء الصلاحية
- **expiryYear**: سنة انتهاء الصلاحية
- **cardType**: نوع البطاقة (فيزا، ماستركارد)
- **lastFourDigits**: آخر أربعة أرقام

#### معلومات إضافية
- **provider**: مقدم الخدمة
- **currency**: العملة المدعومة
- **createdAt**: تاريخ الإضافة
- **updatedAt**: تاريخ آخر تحديث
- **lastUsedAt**: تاريخ آخر استخدام

---

## 🔧 الخدمات والـ Services {#الخدمات}

### خدمات Firebase

#### FirebaseRealService
**الملف**: `lib/services/firebase_real_service.dart`

**الوظائف الرئيسية:**
- تهيئة Firebase للمنصات المختلفة (Android, iOS, macOS)
- إدارة المصادقة مع دعم Google Sign-In
- التعامل مع حالات الخطأ والاستثناءات
- حفظ حالة المستخدم في التخزين المحلي
- إدارة جلسات المستخدمين مع انتهاء صلاحية تلقائي

**الميزات المتقدمة:**
- إعادة المحاولة التلقائية عند فشل الاتصال
- التحقق من صحة الشبكة قبل العمليات
- تسجيل مفصل للأخطاء والعمليات
- دعم المصادقة متعددة العوامل
- إدارة الرموز المميزة وتجديدها

#### FirebaseWebService
**الملف**: `lib/services/firebase_web_service.dart`

**التخصص للويب:**
- تهيئة Firebase JavaScript SDK للمتصفحات
- التعامل مع قيود الأمان في المتصفحات
- دعم Progressive Web App (PWA)
- إدارة إشعارات الويب
- تحسين الأداء للتطبيقات الويب

**الميزات الخاصة:**
- تخزين مؤقت متقدم للمتصفح
- دعم العمل بدون اتصال (Offline)
- ضغط البيانات لتوفير عرض النطاق
- تحسين تحميل الصور والموارد

#### FirestoreDataService
**الملف**: `lib/services/firestore_data_service.dart`

**إدارة البيانات:**
- عمليات CRUD شاملة للمنتجات والفئات
- استعلامات معقدة مع فلترة وترتيب متقدم
- مزامنة البيانات بين المحلي والسحابي
- إدارة التخزين المؤقت للأداء الأمثل
- دعم العمليات المجمعة (Batch Operations)

**تحسين الأداء:**
- تحميل تدريجي للبيانات (Pagination)
- تخزين مؤقت ذكي مع انتهاء صلاحية
- ضغط البيانات قبل الإرسال
- استعلامات محسنة مع فهرسة مناسبة

### خدمات المصادقة

#### AuthServiceEnhanced
**الملف**: `lib/services/auth_service_enhanced.dart`

**المصادقة المتقدمة:**
- دعم طرق مصادقة متعددة
- التحقق من قوة كلمة المرور
- إدارة جلسات المستخدمين
- حماية ضد الهجمات الشائعة
- تسجيل محاولات الدخول المشبوهة

**الأمان:**
- تشفير البيانات الحساسة
- التحقق من صحة الرموز المميزة
- إدارة انتهاء صلاحية الجلسات
- حماية ضد CSRF وXSS
- تسجيل الأنشطة الأمنية

#### AuthServiceInteractive
**الملف**: `lib/services/auth_service_interactive.dart`

**التفاعل مع المستخدم:**
- واجهات تفاعلية لتسجيل الدخول
- رسائل خطأ واضحة ومفيدة
- إرشادات خطوة بخطوة
- دعم إمكانية الوصول
- تجربة مستخدم محسنة

### خدمات البيانات

#### DataService
**الملف**: `lib/services/data_service.dart`

**إدارة البيانات العامة:**
- طبقة تجريد للوصول للبيانات
- دعم مصادر بيانات متعددة
- تخزين مؤقت ذكي
- مزامنة البيانات
- إدارة حالات الخطأ

#### DataSyncService
**الملف**: `lib/services/data_sync_service.dart`

**مزامنة البيانات:**
- مزامنة تلقائية في الخلفية
- حل تضارب البيانات
- دعم العمل بدون اتصال
- قوائم انتظار للعمليات المؤجلة
- إشعارات تحديث البيانات

### خدمات التخزين

#### StorageService
**الملف**: `lib/services/storage_service.dart`

**إدارة الملفات:**
- رفع وتحميل الصور والملفات
- ضغط الصور تلقائياً
- إدارة أنواع الملفات المدعومة
- حماية الملفات بكلمات مرور
- نسخ احتياطية للملفات المهمة

#### HybridStorageService
**الملف**: `lib/services/hybrid_storage_service.dart`

**التخزين المختلط:**
- دمج التخزين المحلي والسحابي
- تحسين استخدام المساحة
- مزامنة تلقائية للملفات
- إدارة النسخ المتعددة
- استرداد الملفات المحذوفة

### خدمات البحث

#### SearchService
**الملف**: `lib/services/search_service.dart`

**البحث المتقدم:**
- بحث نصي سريع ودقيق
- اقتراحات تلقائية أثناء الكتابة
- بحث بالصوت والصورة
- فلترة متقدمة متعددة المعايير
- حفظ عمليات البحث المفضلة

**تحسين النتائج:**
- ترتيب النتائج حسب الصلة
- تعلم من سلوك المستخدم
- اقتراحات ذكية للمنتجات
- بحث دلالي متقدم
- دعم الأخطاء الإملائية

### خدمات الإشعارات

#### NotificationService
**الملف**: `lib/services/notification_service.dart`

**إدارة الإشعارات:**
- إشعارات فورية للطلبات والعروض
- إشعارات مجدولة للتذكيرات
- تخصيص الإشعارات حسب المستخدم
- إدارة تفضيلات الإشعارات
- إحصائيات فتح الإشعارات

**أنواع الإشعارات:**
- إشعارات الطلبات (تأكيد، شحن، توصيل)
- إشعارات العروض والخصومات
- إشعارات المنتجات الجديدة
- تذكيرات السلة المهجورة
- إشعارات إدارية للمدراء

### خدمات حالة التطبيق

#### AppState
**الملف**: `lib/services/app_state.dart`

**إدارة الحالة العامة:**
- حالة المستخدم الحالي
- إعدادات التطبيق العامة
- حالة الاتصال بالشبكة
- حالة التحميل والأخطاء
- تفضيلات المستخدم

**المزامنة:**
- مزامنة الحالة بين الشاشات
- حفظ الحالة عند إغلاق التطبيق
- استرداد الحالة عند فتح التطبيق
- إشعارات تغيير الحالة
- نسخ احتياطية للحالة المهمة

---

## 🎨 التصميم والثيم {#التصميم-والثيم}

### نظام الألوان
**الملف**: `lib/app_properties.dart`

#### الألوان الأساسية
- **اللون الرئيسي**: أزرق (#2196F3) - يمثل الثقة والاحترافية
- **اللون الثانوي**: أصفر ذهبي (#FFC107) - للتمييز والجاذبية
- **لون التمييز**: أخضر (#4CAF50) - للنجاح والتأكيد
- **لون الخلفية**: رمادي فاتح (#F5F5F5) - للراحة البصرية
- **لون النص**: رمادي داكن (#212121) - للوضوح والقراءة

#### ألوان خاصة بمتجر النظارات
- **أزرق العدسات**: (#1976D2) - للمنتجات البصرية
- **ذهبي الإطارات**: (#FFB300) - للفخامة والجودة
- **أخضر الرؤية**: (#388E3C) - للصحة والوضوح
- **أبيض نقي**: (#FFFFFF) - للنظافة والبساطة
- **أسود عميق**: (#000000) - للأناقة والتباين

#### ألوان الحالة
- **النجاح**: أخضر (#4CAF50) - للعمليات الناجحة
- **التحذير**: برتقالي (#FF9800) - للتنبيهات المهمة
- **المعلومات**: أزرق (#2196F3) - للمعلومات العامة
- **الخطر**: أحمر (#F44336) - للأخطاء والتحذيرات

### نظام الخطوط
#### الخط الأساسي
- **عائلة الخط**: Cairo - خط عربي حديث وواضح
- **الأوزان المدعومة**: عادي (400)، متوسط (500)، سميك (600)، عريض (700)
- **دعم الاتجاهات**: RTL للعربية، LTR للإنجليزية
- **أحجام متدرجة**: من 10px إلى 32px

#### أحجام النصوص
- **العناوين الكبيرة**: 32px - للعناوين الرئيسية
- **العناوين المتوسطة**: 24px - للعناوين الفرعية
- **العناوين الصغيرة**: 18px - لعناوين الأقسام
- **النص الأساسي**: 16px - للمحتوى العادي
- **النص الصغير**: 14px - للتفاصيل والملاحظات
- **النص المصغر**: 12px - للمعلومات الإضافية

### نظام التخطيط والمسافات
#### المسافات المعيارية
- **صغيرة**: 8px - للمسافات الضيقة
- **متوسطة**: 16px - للمسافات العادية
- **كبيرة**: 24px - للمسافات الواسعة
- **كبيرة جداً**: 32px - للفصل بين الأقسام

#### الحواف والزوايا
- **زوايا صغيرة**: 4px - للعناصر الصغيرة
- **زوايا متوسطة**: 8px - للبطاقات والأزرار
- **زوايا كبيرة**: 12px - للحاويات الكبيرة
- **زوايا كبيرة جداً**: 16px - للعناصر المميزة

#### الظلال والارتفاع
- **ظل منخفض**: 2dp - للعناصر المسطحة
- **ظل متوسط**: 4dp - للبطاقات العادية
- **ظل عالي**: 8dp - للعناصر المرفوعة
- **ظل عميق**: 16dp - للنوافذ المنبثقة

### مكونات الواجهة

#### الأزرار
- **الزر الرئيسي**: خلفية زرقاء، نص أبيض، زوايا مدورة
- **الزر الثانوي**: حدود زرقاء، نص أزرق، خلفية شفافة
- **الزر المسطح**: بدون خلفية، نص ملون، تأثير عند الضغط
- **الزر العائم**: دائري، ظل عالي، أيقونة مركزية

#### البطاقات
- **بطاقة المنتج**: صورة، عنوان، سعر، تقييم
- **بطاقة الفئة**: صورة خلفية، عنوان، عدد المنتجات
- **بطاقة الطلب**: رقم الطلب، التاريخ، الحالة، الإجمالي
- **بطاقة المعلومات**: أيقونة، عنوان، وصف مختصر

#### حقول الإدخال
- **الحقل العادي**: حدود رمادية، تسمية علوية
- **الحقل المركز**: تسمية داخلية متحركة
- **الحقل المتعدد الأسطر**: قابل للتوسع عمودياً
- **حقل البحث**: أيقونة بحث، زر مسح

#### شرائط التنقل
- **الشريط العلوي**: عنوان الصفحة، أزرار الإجراءات
- **الشريط السفلي**: خمسة تبويبات، أيقونات ونصوص
- **الشريط الجانبي**: قائمة عمودية، أيقونات وتسميات
- **شريط التقدم**: مؤشر خطي أو دائري

### الرسوم المتحركة والتأثيرات

#### انتقالات الصفحات
- **الانزلاق**: من اليمين لليسار (RTL)
- **التلاشي**: ظهور واختفاء تدريجي
- **التكبير**: تكبير من المركز
- **الدوران**: دوران ثلاثي الأبعاد

#### تأثيرات التفاعل
- **الضغط**: تصغير مؤقت عند اللمس
- **التمرير**: تأثير مطاطي عند الوصول للنهاية
- **السحب**: تأثير بصري أثناء السحب
- **التحميل**: دوران أو نبضات متتالية

#### الرسوم المتحركة المخصصة
- **شعار التطبيق**: تكبير مع تلاشي في شاشة البداية
- **إضافة للسلة**: طيران المنتج نحو أيقونة السلة
- **تحديث البيانات**: دوران أيقونة التحديث
- **الإشعارات**: انزلاق من الأعلى مع اهتزاز خفيف

### التصميم المتجاوب

#### نقاط التوقف (Breakpoints)
- **الهاتف الصغير**: أقل من 360px
- **الهاتف العادي**: 360px - 600px
- **التابلت الصغير**: 600px - 900px
- **التابلت الكبير**: 900px - 1200px
- **سطح المكتب**: أكبر من 1200px

#### تكيف التخطيط
- **الشبكة المرنة**: تغيير عدد الأعمدة حسب الشاشة
- **النصوص المتكيفة**: تغيير أحجام الخطوط تلقائياً
- **الصور المرنة**: تكبير وتصغير مع الحفاظ على النسبة
- **القوائم التكيفية**: تحويل من أفقية لعمودية

### إمكانية الوصول (Accessibility)

#### دعم قارئات الشاشة
- **تسميات وصفية**: لجميع العناصر التفاعلية
- **ترتيب منطقي**: للتنقل بالكيبورد
- **نصوص بديلة**: للصور والأيقونات
- **إعلانات الحالة**: للتغييرات المهمة

#### التباين والوضوح
- **تباين عالي**: نسبة 4.5:1 على الأقل
- **أحجام كافية**: 44px للعناصر التفاعلية
- **ألوان مساعدة**: عدم الاعتماد على اللون فقط
- **حركة مخفضة**: احترام تفضيلات النظام

### الثيم الليلي (مستقبلي)
#### ألوان الوضع المظلم
- **الخلفية الرئيسية**: أسود مزرق (#121212)
- **الخلفية الثانوية**: رمادي داكن (#1E1E1E)
- **النص الأساسي**: أبيض (#FFFFFF)
- **النص الثانوي**: رمادي فاتح (#B3B3B3)
- **الألوان المميزة**: نفس الألوان مع تعديل السطوع

#### التبديل التلقائي
- **حسب النظام**: اتباع إعدادات الجهاز
- **حسب الوقت**: تلقائي مع شروق وغروب الشمس
- **يدوي**: تحكم كامل من المستخدم
- **حفظ التفضيل**: تذكر اختيار المستخدم

---

## 🔒 الأمان وقواعد Firebase {#الأمان}

### قواعد أمان Firestore
**الملف**: `firestore.rules`

#### الدوال المساعدة للأمان
```javascript
// التحقق من تسجيل الدخول
function isAuthenticated() {
  return request.auth != null;
}

// التحقق من ملكية المورد
function isOwner(userId) {
  return request.auth != null && request.auth.uid == userId;
}

// التحقق من صلاحيات الإدارة
function isAdmin() {
  return request.auth != null &&
         exists(/databases/$(database)/documents/admins/$(request.auth.uid));
}

// التحقق من صحة البيانات
function isValidData(data) {
  return data.keys().hasAll(['name', 'email']) &&
         data.name is string && data.name.size() > 0 &&
         data.email.matches('.*@.*\\..*');
}
```

#### قواعد المنتجات
- **القراءة**: مسموحة للجميع (بما في ذلك الضيوف)
- **الكتابة**: مقصورة على المدراء فقط
- **الحذف**: مقصور على المدراء مع تسجيل العملية
- **التحديث**: مقصور على المدراء مع التحقق من صحة البيانات

#### قواعد المستخدمين
- **القراءة**: كل مستخدم يقرأ بياناته فقط + المدراء
- **الكتابة**: كل مستخدم يعدل بياناته فقط
- **الإنشاء**: مسموح للمستخدمين المصادق عليهم
- **الحذف**: مقصور على المدراء أو المستخدم نفسه

#### قواعد الطلبات
- **القراءة**: المستخدم يرى طلباته فقط + المدراء يرون الكل
- **الإنشاء**: مسموح للمستخدمين المصادق عليهم
- **التحديث**: المدراء فقط (لتحديث حالة الطلب)
- **الحذف**: مقصور على المدراء مع تسجيل السبب

#### قواعد المراجعات
- **القراءة**: مسموحة للجميع
- **الكتابة**: المستخدمون المصادق عليهم فقط
- **التحديث**: كاتب المراجعة أو المدراء
- **الحذف**: المدراء فقط (مع إمكانية الإخفاء بدلاً من الحذف)

### قواعد أمان Storage
**الملف**: `storage.rules`

#### التحقق من أنواع الملفات
```javascript
// التحقق من نوع الصورة
function isValidImageType() {
  return request.resource.contentType.matches('image/.*');
}

// التحقق من حجم الملف (أقل من 5MB)
function isValidSize() {
  return request.resource.size < 5 * 1024 * 1024;
}

// التحقق من أبعاد الصورة
function isValidImageDimensions() {
  return request.resource.metadata.width != null &&
         request.resource.metadata.height != null &&
         request.resource.metadata.width <= 2048 &&
         request.resource.metadata.height <= 2048;
}
```

#### قواعد صور المنتجات
- **القراءة**: مسموحة للجميع
- **الرفع**: مقصور على المدراء
- **الحذف**: مقصور على المدراء
- **التحديث**: مقصور على المدراء
- **القيود**: صور فقط، أقل من 5MB، أبعاد معقولة

#### قواعد صور المستخدمين
- **القراءة**: مسموحة للجميع (للصور الشخصية)
- **الرفع**: كل مستخدم لمجلده الخاص
- **الحذف**: المستخدم لصوره أو المدراء
- **القيود**: صور فقط، أقل من 2MB، صورة واحدة لكل مستخدم

#### قواعد الوصفات الطبية
- **القراءة**: المستخدم المالك أو المدراء
- **الرفع**: المستخدم المصادق عليه لمجلده
- **الحذف**: المستخدم المالك أو المدراء
- **القيود**: صور أو PDF، أقل من 10MB، تشفير إضافي

### تشفير البيانات

#### البيانات المشفرة
- **كلمات المرور**: تشفير أحادي الاتجاه مع Salt
- **معلومات البطاقات**: تشفير AES-256 مع مفاتيح منفصلة
- **البيانات الشخصية الحساسة**: تشفير قبل التخزين
- **رموز الجلسات**: تشفير وانتهاء صلاحية تلقائي

#### إدارة المفاتيح
- **مفاتيح منفصلة**: لكل نوع من البيانات
- **دوران المفاتيح**: تغيير دوري للمفاتيح
- **تخزين آمن**: في خدمات إدارة المفاتيح
- **نسخ احتياطية**: مشفرة ومحمية

### مراقبة الأمان

#### تسجيل الأنشطة
- **محاولات تسجيل الدخول**: ناجحة وفاشلة
- **العمليات الحساسة**: تعديل البيانات المهمة
- **الوصول للبيانات**: من قبل المدراء
- **الأخطاء الأمنية**: محاولات الوصول غير المصرح

#### التنبيهات الأمنية
- **محاولات دخول مشبوهة**: عدة محاولات فاشلة
- **وصول من مواقع غريبة**: IP جديد أو بلد مختلف
- **عمليات غير عادية**: حجم كبير من الطلبات
- **تغييرات مهمة**: تعديل صلاحيات أو حذف بيانات

### حماية من الهجمات

#### حماية من DDoS
- **تحديد معدل الطلبات**: حد أقصى للطلبات في الدقيقة
- **قوائم سوداء**: حظر IP المشبوهة تلقائياً
- **تحليل الأنماط**: كشف الهجمات المنسقة
- **توزيع الحمولة**: عبر خوادم متعددة

#### حماية من SQL Injection
- **استعلامات محضرة**: استخدام Parameterized Queries
- **تنظيف المدخلات**: فلترة الأحرف الخطيرة
- **التحقق من النوع**: التأكد من نوع البيانات
- **حدود الطول**: تحديد أقصى طول للمدخلات

#### حماية من XSS
- **تنظيف HTML**: إزالة الأكواد الخطيرة
- **ترميز المخرجات**: تحويل الأحرف الخاصة
- **سياسة المحتوى**: Content Security Policy
- **التحقق من المصدر**: Origin والReferer headers

### النسخ الاحتياطية والاسترداد

#### استراتيجية النسخ الاحتياطية
- **نسخ يومية**: للبيانات المتغيرة
- **نسخ أسبوعية**: للبيانات الثابتة
- **نسخ شهرية**: للأرشفة طويلة المدى
- **نسخ فورية**: قبل التحديثات المهمة

#### خطة الاسترداد
- **استرداد جزئي**: لبيانات محددة
- **استرداد كامل**: لكامل النظام
- **اختبار دوري**: للتأكد من صحة النسخ
- **توثيق العملية**: خطوات واضحة ومفصلة

---

## 🌐 دعم المنصات المتعددة {#دعم-المنصات}

### إعدادات Firebase للمنصات
**الملف**: `lib/firebase_options.dart`

#### منصة الويب (Web)
- **API Key**: AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw
- **App ID**: 1:580889516677:web:a7cb9386ef6d04c7fe67ec
- **Project ID**: visionlens-app-5ab70
- **Auth Domain**: visionlens-app-5ab70.firebaseapp.com
- **Storage Bucket**: visionlens-app-5ab70.appspot.com
- **Messaging Sender ID**: 580889516677

#### منصة Android
- **API Key**: نفس مفتاح الويب للتبسيط
- **App ID**: 1:580889516677:android:a7cb9386ef6d04c7fe67ec
- **Package Name**: com.example.visionlensApp
- **SHA-1 Certificate**: مطلوب لـ Google Sign-In
- **ملف google-services.json**: في مجلد android/app

#### منصة iOS
- **API Key**: نفس مفتاح الويب
- **App ID**: 1:580889516677:ios:a7cb9386ef6d04c7fe67ec
- **Bundle ID**: com.example.visionlensApp
- **Team ID**: مطلوب للنشر في App Store
- **ملف GoogleService-Info.plist**: في مجلد ios/Runner

#### منصة macOS
- **API Key**: نفس مفتاح الويب
- **App ID**: 1:580889516677:macos:a7cb9386ef6d04c7fe67ec
- **Bundle ID**: com.example.visionlensApp
- **دعم Catalyst**: للتشغيل على macOS

### خصائص كل منصة

#### تطبيق الويب (Web App)
**المميزات:**
- وصول فوري بدون تحميل
- تحديثات تلقائية فورية
- مشاركة سهلة عبر الروابط
- دعم PWA للعمل بدون اتصال
- تحسين محركات البحث (SEO)

**التحديات:**
- أداء أبطأ من التطبيقات الأصلية
- قيود المتصفح على بعض الميزات
- استهلاك أكبر للبطارية
- تجربة مختلفة حسب المتصفح

**التحسينات المطبقة:**
- تحميل تدريجي للموارد
- تخزين مؤقت متقدم
- ضغط الصور والملفات
- تحسين الكود للأداء

#### تطبيق Android
**المميزات:**
- أداء عالي وسلاسة في التشغيل
- دعم كامل لميزات النظام
- إشعارات فورية ومتقدمة
- تكامل مع خدمات Google
- وصول لجهات الاتصال والكاميرا

**التحديات:**
- تنوع الأجهزة والشاشات
- إصدارات مختلفة من Android
- عملية مراجعة Google Play
- حجم التطبيق وتحديثاته

**التحسينات المطبقة:**
- تصميم متجاوب لجميع الشاشات
- دعم إصدارات Android من API 21
- تحسين حجم APK
- اختبار على أجهزة متنوعة

#### تطبيق iOS
**المميزات:**
- تجربة مستخدم متسقة وعالية الجودة
- أمان متقدم وحماية الخصوصية
- أداء ممتاز على جميع الأجهزة
- تكامل مع نظام Apple البيئي
- جودة عالية في التطبيقات

**التحديات:**
- عملية مراجعة صارمة من Apple
- قيود على بعض الميزات
- تكلفة عضوية المطور
- دعم أجهزة محدودة

**التحسينات المطبقة:**
- اتباع إرشادات Apple للتصميم
- تحسين للشاشات المختلفة (iPhone, iPad)
- دعم Dark Mode
- تحسين استهلاك البطارية

#### تطبيق macOS
**المميزات:**
- تجربة سطح مكتب كاملة
- شاشات كبيرة ومتعددة
- لوحة مفاتيح وماوس
- تعدد المهام المتقدم
- تكامل مع نظام macOS

**التحديات:**
- قاعدة مستخدمين أصغر
- تحديات التصميم للشاشات الكبيرة
- اختلاف أنماط التفاعل
- متطلبات أداء مختلفة

**التحسينات المطبقة:**
- تخطيط متكيف للشاشات الكبيرة
- دعم اختصارات لوحة المفاتيح
- قوائم سياق متقدمة
- تحسين للاستخدام بالماوس

### استراتيجية النشر

#### نشر تطبيق الويب
**Firebase Hosting:**
- استضافة سريعة وآمنة
- شهادة SSL تلقائية
- CDN عالمي للأداء
- نشر تلقائي من Git
- إصدارات متعددة للاختبار

**خطوات النشر:**
1. بناء التطبيق للويب: `flutter build web`
2. تحسين الملفات المولدة
3. رفع للـ Firebase Hosting: `firebase deploy`
4. اختبار الإصدار الجديد
5. تفعيل الإصدار للمستخدمين

#### نشر تطبيق Android
**Google Play Store:**
- وصول لمليارات المستخدمين
- نظام دفع متكامل
- إحصائيات مفصلة
- تحديثات تلقائية
- مراجعات ومعدلات

**خطوات النشر:**
1. بناء APK موقع: `flutter build apk --release`
2. اختبار على أجهزة متنوعة
3. إنشاء حساب مطور Google Play
4. رفع APK مع الوصف والصور
5. مراجعة Google وتفعيل التطبيق

#### نشر تطبيق iOS
**Apple App Store:**
- جودة عالية للتطبيقات
- أمان متقدم
- نظام دفع موثوق
- تكامل مع النظام البيئي
- مراجعة صارمة للجودة

**خطوات النشر:**
1. بناء IPA للإصدار: `flutter build ios --release`
2. اختبار على أجهزة iOS مختلفة
3. إنشاء حساب Apple Developer
4. رفع عبر Xcode أو Application Loader
5. مراجعة Apple وتفعيل التطبيق

### تحسين الأداء لكل منصة

#### تحسينات الويب
- **تحميل تدريجي**: تحميل الموارد حسب الحاجة
- **Service Workers**: للعمل بدون اتصال
- **تخزين مؤقت ذكي**: للموارد الثابتة
- **ضغط Gzip**: لتقليل حجم الملفات
- **تحسين الصور**: WebP وأحجام متعددة

#### تحسينات الموبايل
- **تحسين الذاكرة**: إدارة فعالة للموارد
- **تحسين البطارية**: تقليل العمليات في الخلفية
- **تحسين الشبكة**: ضغط البيانات وتخزين مؤقت
- **تحسين الرسوم**: استخدام GPU للرسوم المعقدة
- **تحسين التخزين**: ضغط البيانات المحلية

#### تحسينات سطح المكتب
- **استغلال الشاشة الكبيرة**: تخطيطات متقدمة
- **تعدد النوافذ**: فتح عدة نوافذ للتطبيق
- **اختصارات المفاتيح**: للمستخدمين المتقدمين
- **قوائم متقدمة**: شريط قوائم وقوائم سياق
- **تكامل النظام**: مع إشعارات وملفات النظام

### اختبار التوافق

#### اختبار المتصفحات
- **Chrome**: المتصفح الأساسي للتطوير
- **Firefox**: اختبار التوافق والأداء
- **Safari**: خاص بأجهزة Apple
- **Edge**: متصفح Microsoft الجديد
- **متصفحات الموبايل**: Chrome Mobile, Safari Mobile

#### اختبار الأجهزة
- **هواتف مختلفة**: أحجام وقوة معالجة متنوعة
- **تابلت**: شاشات أكبر وتفاعل مختلف
- **أجهزة قديمة**: للتأكد من الأداء المقبول
- **شاشات مختلفة**: دقة وكثافة متنوعة
- **أنظمة تشغيل**: إصدارات مختلفة

#### اختبار الشبكة
- **اتصال سريع**: 4G/5G/WiFi سريع
- **اتصال بطيء**: 3G/WiFi بطيء
- **اتصال متقطع**: فقدان الاتصال المؤقت
- **بدون اتصال**: العمل في الوضع المحلي
- **زمن استجابة عالي**: شبكات بعيدة

---

## 🚀 النشر والتطوير {#النشر-والتطوير}

### بيئة التطوير

#### متطلبات النظام
- **Flutter SDK**: الإصدار 3.8.1 أو أحدث
- **Dart SDK**: مدمج مع Flutter
- **Android Studio**: للتطوير على Android
- **Xcode**: للتطوير على iOS (macOS فقط)
- **VS Code**: محرر نصوص خفيف ومرن

#### إعداد المشروع
1. **استنساخ المشروع**: `git clone [repository-url]`
2. **تحميل التبعيات**: `flutter pub get`
3. **إعداد Firebase**: تكوين ملفات الإعدادات
4. **تشغيل التطبيق**: `flutter run`
5. **اختبار الوظائف**: التأكد من عمل جميع الميزات

#### أدوات التطوير المساعدة
- **Flutter Inspector**: لفحص شجرة الويدجت
- **Dart DevTools**: لتحليل الأداء والذاكرة
- **Firebase Console**: لإدارة قاعدة البيانات
- **Git**: لإدارة الإصدارات والتعاون
- **Postman**: لاختبار APIs

### عملية البناء (Build Process)

#### بناء للتطوير
```bash
# تشغيل في وضع التطوير
flutter run

# تشغيل مع Hot Reload
flutter run --hot

# تشغيل على جهاز محدد
flutter run -d [device-id]

# تشغيل مع تفعيل التصحيح
flutter run --debug
```

#### بناء للإنتاج
```bash
# بناء APK للأندرويد
flutter build apk --release

# بناء App Bundle للأندرويد
flutter build appbundle --release

# بناء للويب
flutter build web --release

# بناء لـ iOS
flutter build ios --release

# بناء لـ macOS
flutter build macos --release
```

### إدارة الإصدارات

#### نظام الترقيم
- **الإصدار الحالي**: 1.0.0+1
- **نمط الترقيم**: MAJOR.MINOR.PATCH+BUILD
- **الإصدارات الرئيسية**: تغييرات كبيرة أو ميزات جديدة
- **الإصدارات الفرعية**: تحسينات وميزات صغيرة
- **إصلاحات الأخطاء**: إصلاحات سريعة وضرورية

#### سجل التغييرات (Changelog)
```markdown
## [1.0.0] - 2024-01-15
### Added
- إطلاق التطبيق الأول
- نظام المصادقة مع Google Sign-In
- إدارة المنتجات والفئات
- نظام التسوق والطلبات
- لوحة تحكم إدارية

### Changed
- تحسين أداء تحميل الصور
- تحديث واجهة المستخدم

### Fixed
- إصلاح مشكلة تسجيل الدخول
- إصلاح مشكلة عرض الأسعار
```

### اختبار التطبيق

#### أنواع الاختبارات
- **اختبارات الوحدة**: للدوال والكلاسات المفردة
- **اختبارات الويدجت**: لمكونات الواجهة
- **اختبارات التكامل**: للتدفقات الكاملة
- **اختبارات الأداء**: لقياس السرعة والذاكرة
- **اختبارات المستخدم**: تجربة حقيقية

#### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/unit_tests/

# تشغيل اختبارات التكامل
flutter drive --target=test_driver/app.dart

# تشغيل اختبارات الأداء
flutter test --coverage
```

### نشر التطبيق

#### نشر تطبيق الويب
```bash
# بناء للويب
flutter build web --release

# نشر على Firebase Hosting
firebase deploy --only hosting

# نشر إصدار تجريبي
firebase hosting:channel:deploy preview

# تفعيل الإصدار الجديد
firebase hosting:channel:deploy live
```

#### نشر تطبيق Android
1. **إنشاء مفتاح التوقيع**:
```bash
keytool -genkey -v -keystore ~/upload-keystore.jks \
-keyalg RSA -keysize 2048 -validity 10000 \
-alias upload
```

2. **تكوين ملف key.properties**:
```properties
storePassword=<password>
keyPassword=<password>
keyAlias=upload
storeFile=<path-to-keystore>
```

3. **بناء وتوقيع التطبيق**:
```bash
flutter build appbundle --release
```

4. **رفع على Google Play Console**

#### نشر تطبيق iOS
1. **إعداد شهادات Apple Developer**
2. **تكوين Provisioning Profiles**
3. **بناء التطبيق**:
```bash
flutter build ios --release
```
4. **رفع عبر Xcode أو Transporter**
5. **مراجعة App Store Connect**

### مراقبة الأداء

#### Firebase Performance Monitoring
- **مراقبة أوقات التحميل**: للشاشات المختلفة
- **مراقبة طلبات الشبكة**: سرعة الاستجابة
- **مراقبة استهلاك الذاكرة**: تجنب تسريب الذاكرة
- **مراقبة معدل الإطارات**: سلاسة الرسوم المتحركة
- **تتبع الأخطاء**: Firebase Crashlytics

#### تحليل البيانات
- **Firebase Analytics**: سلوك المستخدمين
- **Google Analytics**: إحصائيات الويب
- **تقارير الأداء**: يومية وأسبوعية وشهرية
- **تحليل التحويلات**: من زيارة إلى شراء
- **تحليل الاحتفاظ**: عودة المستخدمين

### صيانة التطبيق

#### التحديثات الدورية
- **تحديث التبعيات**: Flutter وPackages
- **تحديث Firebase**: إصدارات جديدة
- **إصلاح الأخطاء**: حسب تقارير المستخدمين
- **تحسين الأداء**: بناءً على البيانات
- **إضافة ميزات جديدة**: حسب احتياجات السوق

#### النسخ الاحتياطية
- **نسخ احتياطية للكود**: Git repositories
- **نسخ احتياطية للبيانات**: Firebase backups
- **نسخ احتياطية للإعدادات**: تصدير التكوينات
- **خطة الاسترداد**: في حالة الطوارئ
- **اختبار الاستعادة**: دوري ومنتظم

---

## ⚡ الميزات المتقدمة {#الميزات-المتقدمة}

### البحث الذكي والفلترة

#### محرك البحث المتقدم
- **البحث النصي**: في أسماء ووصف المنتجات
- **البحث الصوتي**: تحويل الصوت لنص للبحث
- **البحث بالصورة**: رفع صورة للعثور على منتجات مشابهة
- **الاقتراحات التلقائية**: أثناء الكتابة
- **تصحيح الأخطاء الإملائية**: اقتراح الكلمات الصحيحة
- **البحث الدلالي**: فهم معنى الاستعلام

#### نظام الفلترة المتقدم
- **فلترة متعددة المعايير**: السعر، العلامة التجارية، التقييم
- **فلترة حسب المواصفات**: مادة الإطار، نوع العدسة
- **فلترة جغرافية**: المنتجات المتاحة في منطقة معينة
- **فلترة زمنية**: المنتجات الجديدة، العروض المحدودة
- **حفظ الفلاتر**: للاستخدام المستقبلي
- **فلاتر ذكية**: بناءً على تاريخ التصفح

### نظام التوصيات الذكي

#### خوارزميات التوصية
- **التوصية بناءً على التصفح**: المنتجات المشاهدة مؤخراً
- **التوصية بناءً على الشراء**: منتجات مشابهة للمشتريات السابقة
- **التوصية الاجتماعية**: ما يشتريه مستخدمون مشابهون
- **التوصية الموسمية**: منتجات مناسبة للوقت الحالي
- **التوصية الشخصية**: بناءً على الملف الشخصي والتفضيلات

#### تخصيص التجربة
- **الصفحة الرئيسية المخصصة**: محتوى مختلف لكل مستخدم
- **ترتيب النتائج**: حسب اهتمامات المستخدم
- **العروض المخصصة**: خصومات على المنتجات المفضلة
- **الإشعارات الذكية**: في الأوقات المناسبة للمستخدم
- **المحتوى التفاعلي**: تكيف مع سلوك المستخدم

### تقنيات الواقع المعزز (AR)

#### تجربة النظارات الافتراضية
- **كاميرا الوجه**: التقاط صورة للوجه
- **كشف ملامح الوجه**: تحديد نقاط مرجعية
- **تركيب النظارات**: وضع النظارة على الوجه افتراضياً
- **تعديل الحجم**: تكييف حجم النظارة مع الوجه
- **تغيير الألوان**: تجربة ألوان مختلفة
- **حفظ ومشاركة**: الصور مع النظارات المجربة

#### قياس الوجه الذكي
- **قياس عرض الوجه**: لاختيار الحجم المناسب
- **قياس المسافة بين العينين**: لتحديد عرض الجسر
- **تحليل شكل الوجه**: لاقتراح أشكال إطارات مناسبة
- **كشف لون البشرة**: لاقتراح ألوان متناسقة
- **تحليل ملامح الوجه**: لتوصيات شخصية

### الذكاء الاصطناعي والتعلم الآلي

#### تحليل سلوك المستخدم
- **تتبع النقرات**: أين ينقر المستخدمون أكثر
- **تحليل وقت التصفح**: كم يقضي في كل صفحة
- **مسار التنقل**: كيف يتنقل عبر التطبيق
- **نقاط الخروج**: أين يترك المستخدمون التطبيق
- **تحليل التحويل**: من تصفح إلى شراء

#### التنبؤ والتوقعات
- **توقع الطلب**: على المنتجات المختلفة
- **توقع الاتجاهات**: في الموضة والتصميم
- **توقع سلوك المستخدم**: احتمالية الشراء
- **توقع المخزون**: متى ينفد منتج معين
- **توقع الأسعار**: أفضل أوقات للعروض

### تكامل مع الخدمات الخارجية

#### خدمات الدفع المتقدمة
- **Apple Pay**: للدفع السريع على iOS
- **Google Pay**: للدفع السريع على Android
- **PayPal**: محفظة رقمية عالمية
- **Stripe**: معالجة دفع متقدمة
- **محافظ محلية**: حسب السوق المستهدف

#### خدمات الشحن والتوصيل
- **تكامل مع شركات الشحن**: تتبع فوري للطلبات
- **حساب تكلفة الشحن**: بناءً على الوزن والمسافة
- **جدولة التوصيل**: اختيار وقت مناسب للعميل
- **إشعارات التوصيل**: تحديثات فورية عن الطلب
- **خريطة التتبع**: موقع الطلب في الوقت الفعلي

#### خدمات التحليل المتقدمة
- **Google Analytics**: تحليل شامل للموقع
- **Facebook Pixel**: تتبع التحويلات من الإعلانات
- **Hotjar**: تسجيل جلسات المستخدمين
- **Mixpanel**: تحليل سلوك المستخدم المتقدم
- **Amplitude**: تحليل رحلة المستخدم

### ميزات إمكانية الوصول المتقدمة

#### دعم ذوي الاحتياجات الخاصة
- **قارئ الشاشة**: دعم كامل لـ TalkBack وVoiceOver
- **التنقل بالكيبورد**: للمستخدمين الذين لا يستطيعون استخدام اللمس
- **تكبير النص**: أحجام خطوط قابلة للتعديل
- **تباين عالي**: ألوان واضحة للمستخدمين ضعاف البصر
- **تقليل الحركة**: للمستخدمين الحساسين للرسوم المتحركة

#### دعم اللغات المتعددة
- **العربية**: اللغة الأساسية مع دعم RTL كامل
- **الإنجليزية**: للوصول لجمهور أوسع
- **لغات إضافية**: حسب السوق المستهدف
- **ترجمة تلقائية**: للمحتوى الذي ينشئه المستخدمون
- **تبديل اللغة**: سهل وفوري

### أمان متقدم وحماية البيانات

#### التشفير المتقدم
- **تشفير من النهاية للنهاية**: للرسائل الحساسة
- **تشفير قاعدة البيانات**: جميع البيانات مشفرة
- **تشفير النقل**: HTTPS لجميع الاتصالات
- **تشفير التخزين المحلي**: البيانات المحفوظة على الجهاز
- **إدارة المفاتيح**: نظام آمن لإدارة مفاتيح التشفير

#### مصادقة متعددة العوامل
- **رمز SMS**: إرسال رمز للهاتف
- **تطبيق المصادقة**: Google Authenticator أو مشابه
- **البريد الإلكتروني**: رمز تأكيد عبر البريد
- **البصمة**: على الأجهزة المدعومة
- **التعرف على الوجه**: للأجهزة المتقدمة

### تحليلات الأعمال المتقدمة

#### لوحة معلومات تنفيذية
- **مؤشرات الأداء الرئيسية**: KPIs في الوقت الفعلي
- **تحليل المبيعات**: اتجاهات وأنماط البيع
- **تحليل العملاء**: سلوك وتفضيلات المستخدمين
- **تحليل المنتجات**: أداء كل منتج وفئة
- **تحليل التسويق**: فعالية الحملات الإعلانية

#### تقارير مخصصة
- **تقارير المبيعات**: يومية، أسبوعية، شهرية
- **تقارير المخزون**: مستويات المخزون والتنبؤات
- **تقارير العملاء**: تحليل قاعدة العملاء
- **تقارير الأداء**: سرعة التطبيق والأخطاء
- **تقارير مالية**: الأرباح والخسائر والتدفق النقدي

---

## 🔧 استكشاف الأخطاء وحلولها {#استكشاف-الأخطاء}

### مشاكل شائعة وحلولها

#### مشاكل تسجيل الدخول
**المشكلة**: فشل تسجيل الدخول بـ Google
**الأسباب المحتملة**:
- عدم تكوين Google Sign-In بشكل صحيح
- مشكلة في شهادات SHA-1
- عدم تفعيل Google Sign-In في Firebase Console

**الحلول**:
1. التحقق من ملف google-services.json
2. إعادة إنشاء شهادات SHA-1
3. تفعيل Google Sign-In في Firebase Console
4. التأكد من تطابق Package Name

#### مشاكل تحميل البيانات
**المشكلة**: عدم ظهور المنتجات أو البيانات
**الأسباب المحتملة**:
- مشكلة في الاتصال بـ Firestore
- قواعد الأمان تمنع الوصول
- خطأ في استعلام البيانات
- مشكلة في الشبكة

**الحلول**:
1. التحقق من اتصال الإنترنت
2. مراجعة قواعد Firestore Security Rules
3. فحص استعلامات البيانات في الكود
4. التحقق من إعدادات Firebase

#### مشاكل رفع الصور
**المشكلة**: فشل رفع الصور لـ Firebase Storage
**الأسباب المحتملة**:
- حجم الصورة كبير جداً
- نوع الملف غير مدعوم
- قواعد Storage تمنع الرفع
- مشكلة في الصلاحيات

**الحلول**:
1. ضغط الصور قبل الرفع
2. التحقق من نوع الملف المدعوم
3. مراجعة قواعد Firebase Storage
4. التأكد من تسجيل دخول المستخدم

### أدوات التشخيص

#### سجلات التطبيق (Logs)
```dart
// تفعيل السجلات المفصلة
import 'package:flutter/foundation.dart';

void debugLog(String message) {
  if (kDebugMode) {
    print('🐛 DEBUG: $message');
  }
}

void errorLog(String message, [dynamic error]) {
  if (kDebugMode) {
    print('❌ ERROR: $message');
    if (error != null) {
      print('Details: $error');
    }
  }
}
```

#### مراقبة الأداء
- **Flutter Inspector**: لفحص شجرة الويدجت
- **Performance Overlay**: لمراقبة معدل الإطارات
- **Memory Usage**: لتتبع استهلاك الذاكرة
- **Network Monitoring**: لمراقبة طلبات الشبكة
- **Firebase Performance**: لمراقبة الأداء في الإنتاج

#### اختبار الاتصال
```dart
// اختبار الاتصال بـ Firebase
Future<bool> testFirebaseConnection() async {
  try {
    await FirebaseFirestore.instance
        .collection('test')
        .limit(1)
        .get();
    return true;
  } catch (e) {
    debugLog('Firebase connection failed: $e');
    return false;
  }
}
```

### إرشادات الصيانة

#### تحديث التبعيات
```bash
# تحديث جميع الحزم
flutter pub upgrade

# تحديث Flutter SDK
flutter upgrade

# تحقق من التبعيات القديمة
flutter pub outdated

# تنظيف المشروع
flutter clean
flutter pub get
```

#### مراقبة الأخطاء
- **Firebase Crashlytics**: لتتبع الأخطاء في الإنتاج
- **Sentry**: نظام مراقبة أخطاء متقدم
- **تقارير المستخدمين**: نظام للإبلاغ عن المشاكل
- **سجلات الخادم**: مراقبة أخطاء الخادم
- **تنبيهات فورية**: عند حدوث أخطاء حرجة

#### نصائح الأداء
1. **تحسين الصور**: استخدام أحجام مناسبة وضغط
2. **تحسين الاستعلامات**: تجنب استعلامات غير ضرورية
3. **التخزين المؤقت**: حفظ البيانات المتكررة محلياً
4. **تحميل تدريجي**: تحميل البيانات حسب الحاجة
5. **إدارة الذاكرة**: تنظيف الموارد غير المستخدمة

### دعم المستخدمين

#### قنوات الدعم
- **الدعم داخل التطبيق**: نظام تذاكر مدمج
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: خط ساخن للدعم الفوري
- **الدردشة المباشرة**: دعم فوري أثناء ساعات العمل
- **قاعدة المعرفة**: أسئلة شائعة وحلول

#### توثيق المستخدم
- **دليل البدء السريع**: للمستخدمين الجدد
- **فيديوهات تعليمية**: شرح الميزات الرئيسية
- **أسئلة شائعة**: حلول للمشاكل الشائعة
- **نصائح وحيل**: لاستخدام أفضل للتطبيق
- **تحديثات الميزات**: شرح الميزات الجديدة

---

## 📞 معلومات الاتصال والدعم

### معلومات المشروع
- **اسم المشروع**: VisionLens App
- **الإصدار الحالي**: 1.0.0+1
- **تاريخ الإطلاق**: 2025
- **المطور**: فريق VisionLens
- **الترخيص**: حقوق محفوظة

### روابط مهمة
- **Firebase Console**: https://console.firebase.google.com/project/visionlens-app-5ab70
- **Firestore Database**: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore
- **Firebase Storage**: https://console.firebase.google.com/project/visionlens-app-5ab70/storage
- **Firebase Authentication**: https://console.firebase.google.com/project/visionlens-app-5ab70/authentication

### معلومات التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: <EMAIL>
- **الهاتف**: +964123456789
- **العنوان**: العراق - بغداد

---

*هذا الكتيب يغطي جميع جوانب تطبيق VisionLens بشكل شامل ومفصل. يمكن الرجوع إليه في أي وقت لفهم أي جزء من التطبيق أو لتطويره وصيانته.*

**© 2024 VisionLens. جميع الحقوق محفوظة.**