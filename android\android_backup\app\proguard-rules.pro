# قواعد ProGuard لتطبيق VisionLens
# هذه القواعد تحمي الكود من التلاعب وتحسن الأداء

# الحفاظ على معلومات التصحيح للأخطاء
-keepattributes SourceFile,LineNumberTable
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Firebase - حماية شاملة
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-keep class com.google.gson.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Flutter - حماية كاملة للإطار
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.** { *; }

# Dart/Flutter - حماية الكود المترجم
-keep class io.flutter.plugins.** { *; }
-keep class androidx.lifecycle.** { *; }

# JSON Serialization - للحفاظ على نماذج البيانات
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Image Loading - للصور والكاش
-keep class com.bumptech.glide.** { *; }
-keep class androidx.** { *; }

# Network - للاتصال بالإنترنت
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# Location Services - لخدمات الموقع
-keep class com.google.android.gms.location.** { *; }
-keep class com.google.android.gms.maps.** { *; }

# Camera and Image Picker - للكاميرا واختيار الصور
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Notifications - للإشعارات
-keep class androidx.work.** { *; }
-keep class com.google.firebase.messaging.** { *; }

# WebView - إذا كان التطبيق يستخدم WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# تحسينات عامة
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# منع تحذيرات غير مهمة
-dontwarn java.lang.invoke.**
-dontwarn javax.annotation.**
-dontwarn kotlin.**
-dontwarn kotlinx.**
