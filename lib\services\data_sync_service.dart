import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'firebase_mock_service.dart';
import 'storage_service.dart';
import 'auth_service_mock.dart';
import 'app_state.dart';
import '../models/product.dart';
import '../models/order.dart' as order_model;

class DataSyncService {
  static final DataSyncService _instance = DataSyncService._internal();
  factory DataSyncService() => _instance;
  DataSyncService._internal();

  static bool _isOnline = false;
  static bool get isOnline => _isOnline;

  static StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  static Timer? _syncTimer;

  // تهيئة الخدمة
  static Future<void> initialize() async {
    await _checkConnectivity();
    _startConnectivityListener();
    _startPeriodicSync();

    // مزامنة أولية
    if (_isOnline) {
      await syncAllData();
    }
  }

  // التحقق من الاتصال
  static Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      _isOnline = connectivityResult != ConnectivityResult.none;

      if (_isOnline) {
        // التحقق من Firebase Mock Service
        _isOnline = true; // دائماً متاح في النظام المحاكي
      }
    } catch (e) {
      _isOnline = false;
      if (kDebugMode) print('خطأ في التحقق من الاتصال: $e');
    }
  }

  // الاستماع لتغييرات الاتصال
  static void _startConnectivityListener() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((
      ConnectivityResult result,
    ) async {
      final wasOnline = _isOnline;
      await _checkConnectivity();

      if (!wasOnline && _isOnline) {
        // عاد الاتصال - مزامنة البيانات
        if (kDebugMode) print('عاد الاتصال - بدء المزامنة');
        await syncAllData();
      }
    });
  }

  // مزامنة دورية
  static void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (_isOnline && AuthService.isLoggedIn) {
        await syncUserData();
      }
    });
  }

  // مزامنة جميع البيانات
  static Future<void> syncAllData() async {
    if (!_isOnline) return;

    try {
      await Future.wait([
        syncProducts(),
        syncCategories(),
        if (AuthService.isLoggedIn) syncUserData(),
      ]);
      if (kDebugMode) print('تمت مزامنة جميع البيانات بنجاح');
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة البيانات: $e');
    }
  }

  // مزامنة المنتجات
  static Future<void> syncProducts() async {
    if (!_isOnline) return;

    try {
      final firebaseProducts = await FirebaseMockService.getAllProducts();

      if (firebaseProducts.isNotEmpty) {
        // حفظ في التخزين المحلي
        await StorageService.saveProducts(firebaseProducts);

        // تحديث حالة التطبيق
        // final appState = AppState();
        // سيتم تحديث هذا عندما نضيف دالة setProducts
        if (kDebugMode) print('تم تحديث ${firebaseProducts.length} منتج');
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة المنتجات: $e');
    }
  }

  // مزامنة الفئات
  static Future<void> syncCategories() async {
    if (!_isOnline) return;

    try {
      final firebaseCategories = await FirebaseMockService.getAllCategories();

      if (firebaseCategories.isNotEmpty) {
        await StorageService.saveCategories(firebaseCategories);
        if (kDebugMode) print('تم تحديث ${firebaseCategories.length} فئة');
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة الفئات: $e');
    }
  }

  // مزامنة بيانات المستخدم
  static Future<void> syncUserData() async {
    if (!_isOnline || !AuthService.isLoggedIn) return;

    try {
      final userId = AuthService.currentUser!.id;

      await Future.wait([
        syncUserCart(userId),
        syncUserWishlist(userId),
        syncUserOrders(userId),
      ]);

      if (kDebugMode) print('تمت مزامنة بيانات المستخدم');
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة بيانات المستخدم: $e');
    }
  }

  // مزامنة سلة المستخدم
  static Future<void> syncUserCart(String userId) async {
    try {
      // رفع السلة المحلية إلى Firebase
      final localCart = await StorageService.loadCart();
      if (localCart.isNotEmpty) {
        for (final item in localCart) {
          await FirebaseMockService.addToCart(userId, item.toJson());
        }
        // مسح السلة المحلية بعد الرفع
        await StorageService.saveCart([]);
      }

      // تحميل السلة من Firebase
      final firebaseCart = await FirebaseMockService.getUserCart(userId);
      if (firebaseCart.isNotEmpty) {
        final cartItems = firebaseCart
            .map((item) => CartItem.fromJson(item))
            .toList();
        await StorageService.saveCart(cartItems);

        // تحديث حالة التطبيق
        final appState = AppState();
        for (final item in cartItems) {
          appState.addCartItem(item);
        }
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة السلة: $e');
    }
  }

  // مزامنة مفضلة المستخدم
  static Future<void> syncUserWishlist(String userId) async {
    try {
      // رفع المفضلة المحلية إلى Firebase
      final localWishlist = await StorageService.loadWishlist();
      if (localWishlist.isNotEmpty) {
        for (final item in localWishlist) {
          await FirebaseMockService.addToWishlist(userId, item.product.id);
        }
        // مسح المفضلة المحلية بعد الرفع
        await StorageService.saveWishlist([]);
      }

      // تحميل المفضلة من Firebase
      final firebaseWishlist = await FirebaseMockService.getUserWishlist(
        userId,
      );
      if (firebaseWishlist.isNotEmpty) {
        final wishlistItems = <WishlistItem>[];

        for (final item in firebaseWishlist) {
          final product = await FirebaseMockService.getProductById(
            item['productId'],
          );
          if (product != null) {
            wishlistItems.add(
              WishlistItem(
                id: item['id'],
                product: product,
                addedAt: DateTime.parse(item['addedAt']),
              ),
            );
          }
        }

        await StorageService.saveWishlist(wishlistItems);

        // تحديث حالة التطبيق
        final appState = AppState();
        for (final item in wishlistItems) {
          appState.addToWishlist(item.product);
        }
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة المفضلة: $e');
    }
  }

  // مزامنة طلبات المستخدم
  static Future<void> syncUserOrders(String userId) async {
    try {
      final firebaseOrders = await FirebaseMockService.getUserOrders(userId);
      if (firebaseOrders.isNotEmpty) {
        await StorageService.saveOrders(firebaseOrders);
        if (kDebugMode) print('تم تحديث ${firebaseOrders.length} طلب');
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في مزامنة الطلبات: $e');
    }
  }

  // إضافة للسلة مع مزامنة
  static Future<bool> addToCart(CartItem cartItem) async {
    try {
      // حفظ محلي أولاً
      final appState = AppState();
      appState.addCartItem(cartItem);
      await StorageService.saveCart(appState.cartItems);

      // رفع إلى Firebase إذا كان متصل
      if (_isOnline && AuthService.isLoggedIn) {
        final userId = AuthService.currentUser!.id;
        await FirebaseMockService.addToCart(userId, cartItem.toJson());
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في إضافة للسلة: $e');
      return false;
    }
  }

  // إضافة للمفضلة مع مزامنة
  static Future<bool> addToWishlist(Product product) async {
    try {
      // حفظ محلي أولاً
      final appState = AppState();
      appState.addToWishlist(product);
      await StorageService.saveWishlist(appState.wishlistItems);

      // رفع إلى Firebase إذا كان متصل
      if (_isOnline && AuthService.isLoggedIn) {
        final userId = AuthService.currentUser!.id;
        await FirebaseMockService.addToWishlist(userId, product.id);
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في إضافة للمفضلة: $e');
      return false;
    }
  }

  // إنشاء طلب مع مزامنة
  static Future<String?> createOrder(order_model.Order order) async {
    try {
      String? orderId;

      // إنشاء في Firebase إذا كان متصل
      if (_isOnline) {
        orderId = await FirebaseMockService.createOrder(order);
      }

      // حفظ محلي
      if (orderId != null) {
        final orderWithId = order.copyWith(id: orderId);
        await StorageService.saveOrder(orderWithId);
      } else {
        // حفظ مؤقت محلي
        await StorageService.saveOrder(order);
      }

      return orderId ?? order.id;
    } catch (e) {
      if (kDebugMode) print('خطأ في إنشاء الطلب: $e');
      return null;
    }
  }

  // تنظيف الموارد
  static void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
  }

  // إعادة تعيين الخدمة
  static Future<void> reset() async {
    dispose();
    _isOnline = false;
    await initialize();
  }

  // فرض المزامنة
  static Future<void> forcSync() async {
    await _checkConnectivity();
    if (_isOnline) {
      await syncAllData();
    }
  }

  // الحصول على حالة المزامنة
  static Map<String, dynamic> getSyncStatus() {
    return {
      'isOnline': _isOnline,
      'isLoggedIn': AuthService.isLoggedIn,
      'lastSyncTime': DateTime.now().toIso8601String(),
    };
  }
}
