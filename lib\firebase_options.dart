// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw',
    appId: '1:580889516677:web:a7cb9386ef6d04c7fe67ec',
    messagingSenderId: '580889516677',
    projectId: 'visionlens-app-5ab70',
    authDomain: 'visionlens-app-5ab70.firebaseapp.com',
    storageBucket: 'visionlens-app-5ab70.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAh3qrLgdXNtO6dr--gOKj4iavkRRRHVC4',
    appId: '1:580889516677:android:fa3e2238eb6aa6a7fe67ec',
    messagingSenderId: '580889516677',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyATPmAnMsvCxqaHzV9qWbyq2CKBHcxid2M',
    appId: '1:580889516677:ios:c4e5ddaed40b88f3fe67ec',
    messagingSenderId: '580889516677',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.firebasestorage.app',
    iosBundleId: 'com.visionlens.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyATPmAnMsvCxqaHzV9qWbyq2CKBHcxid2M',
    appId: '1:580889516677:macos:c4e5ddaed40b88f3fe67ec',
    messagingSenderId: '580889516677',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.firebasestorage.app',
    iosBundleId: 'com.visionlens.app',
  );
}
