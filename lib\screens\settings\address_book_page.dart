import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../models/user_simple.dart' as user_model;

class AddressBookPage extends StatefulWidget {
  const AddressBookPage({super.key});

  @override
  State<AddressBookPage> createState() => _AddressBookPageState();
}

class _AddressBookPageState extends State<AddressBookPage> {
  List<user_model.Address> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    // محاكاة تحميل العناوين
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _addresses = _generateMockAddresses();
      _isLoading = false;
    });
  }

  List<user_model.Address> _generateMockAddresses() {
    return [
      const user_model.Address(
        id: '1',
        title: 'المنزل',
        firstName: 'أحمد',
        lastName: 'محمد',
        phone: '+964 ************',
        country: 'العراق',
        city: 'بغداد',
        district: 'الكرادة',
        street: 'شارع الكرادة الداخلية',
        buildingNumber: '123',
        apartmentNumber: '4',
        isDefault: true,
        type: user_model.AddressType.home,
      ),
      const user_model.Address(
        id: '2',
        title: 'العمل',
        firstName: 'أحمد',
        lastName: 'محمد',
        phone: '+964 ************',
        country: 'العراق',
        city: 'بغداد',
        district: 'المنصور',
        street: 'شارع الأميرات',
        buildingNumber: '456',
        isDefault: false,
        type: user_model.AddressType.work,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('دفتر العناوين'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
        actions: [
          IconButton(onPressed: _addNewAddress, icon: const Icon(Icons.add)),
        ],
      ),
      body: _isLoading ? _buildLoadingState() : _buildAddressList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewAddress,
        backgroundColor: AppColors.primaryColor,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildAddressList() {
    if (_addresses.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        return _buildAddressCard(_addresses[index], index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.location_on_outlined,
              size: 60,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد عناوين محفوظة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف عنوان جديد لتسهيل عملية التوصيل',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _addNewAddress,
            icon: const Icon(Icons.add),
            label: const Text('إضافة عنوان جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressCard(user_model.Address address, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        border: address.isDefault
            ? Border.all(color: AppColors.primaryColor, width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getTypeColor(address.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getTypeIcon(address.type),
                        size: 16,
                        color: _getTypeColor(address.type),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        address.typeDescription,
                        style: TextStyle(
                          color: _getTypeColor(address.type),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                if (address.isDefault)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'افتراضي',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                PopupMenuButton<String>(
                  onSelected: (value) =>
                      _handleMenuAction(value, address, index),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    if (!address.isDefault)
                      const PopupMenuItem(
                        value: 'setDefault',
                        child: Row(
                          children: [
                            Icon(Icons.star, size: 20),
                            SizedBox(width: 8),
                            Text('جعل افتراضي'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            size: 20,
                            color: AppColors.errorColor,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: TextStyle(color: AppColors.errorColor),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // اسم المستلم
            Text(
              '${address.firstName} ${address.lastName}',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            // العنوان
            Text(
              address.fullAddress,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.secondaryText),
            ),

            const SizedBox(height: 8),

            // رقم الهاتف
            Row(
              children: [
                const Icon(
                  Icons.phone,
                  size: 16,
                  color: AppColors.secondaryText,
                ),
                const SizedBox(width: 4),
                Text(
                  address.phone,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(user_model.AddressType type) {
    switch (type) {
      case user_model.AddressType.home:
        return AppColors.primaryColor;
      case user_model.AddressType.work:
        return AppColors.warning;
      case user_model.AddressType.other:
        return AppColors.success;
    }
  }

  IconData _getTypeIcon(user_model.AddressType type) {
    switch (type) {
      case user_model.AddressType.home:
        return Icons.home;
      case user_model.AddressType.work:
        return Icons.work;
      case user_model.AddressType.other:
        return Icons.location_on;
    }
  }

  void _handleMenuAction(String action, user_model.Address address, int index) {
    switch (action) {
      case 'edit':
        _editAddress(address, index);
        break;
      case 'setDefault':
        _setDefaultAddress(index);
        break;
      case 'delete':
        _deleteAddress(index);
        break;
    }
  }

  void _addNewAddress() {
    _showAddressForm();
  }

  void _editAddress(user_model.Address address, int index) {
    _showAddressForm(address: address, index: index);
  }

  void _setDefaultAddress(int index) {
    setState(() {
      // إزالة الافتراضي من جميع العناوين
      for (int i = 0; i < _addresses.length; i++) {
        _addresses[i] = _addresses[i].copyWith(isDefault: i == index);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تعيين العنوان كافتراضي'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _deleteAddress(int index) {
    final address = _addresses[index];

    if (address.isDefault && _addresses.length > 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'لا يمكن حذف العنوان الافتراضي. قم بتعيين عنوان آخر كافتراضي أولاً',
          ),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العنوان'),
        content: Text('هل أنت متأكد من حذف عنوان "${address.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _addresses.removeAt(index);
              });
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف عنوان "${address.title}"'),
                  backgroundColor: AppColors.success,
                  action: SnackBarAction(
                    label: 'تراجع',
                    onPressed: () {
                      setState(() {
                        _addresses.insert(index, address);
                      });
                    },
                  ),
                ),
              );
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showAddressForm({user_model.Address? address, int? index}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddressFormSheet(
        address: address,
        onSave: (newAddress) {
          setState(() {
            if (index != null) {
              _addresses[index] = newAddress;
            } else {
              _addresses.add(newAddress);
            }
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                index != null ? 'تم تحديث العنوان' : 'تم إضافة العنوان الجديد',
              ),
              backgroundColor: AppColors.success,
            ),
          );
        },
      ),
    );
  }
}

class AddressFormSheet extends StatefulWidget {
  final user_model.Address? address;
  final Function(user_model.Address) onSave;

  const AddressFormSheet({super.key, this.address, required this.onSave});

  @override
  State<AddressFormSheet> createState() => _AddressFormSheetState();
}

class _AddressFormSheetState extends State<AddressFormSheet> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _titleController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _countryController = TextEditingController();
  final _cityController = TextEditingController();
  final _districtController = TextEditingController();
  final _streetController = TextEditingController();
  final _buildingController = TextEditingController();
  final _apartmentController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _notesController = TextEditingController();

  user_model.AddressType _selectedType = user_model.AddressType.home;
  bool _isDefault = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAddressData();
  }

  void _loadAddressData() {
    if (widget.address != null) {
      final address = widget.address!;
      _titleController.text = address.title;
      _firstNameController.text = address.firstName;
      _lastNameController.text = address.lastName;
      _phoneController.text = address.phone;
      _countryController.text = address.country;
      _cityController.text = address.city;
      _districtController.text = address.district;
      _streetController.text = address.street;
      _buildingController.text = address.buildingNumber;
      _apartmentController.text = address.apartmentNumber ?? '';
      _postalCodeController.text = address.postalCode ?? '';
      _notesController.text = address.additionalInfo ?? '';
      _selectedType = address.type;
      _isDefault = address.isDefault;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
          topRight: Radius.circular(AppDimensions.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.grey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.address != null ? 'تعديل العنوان' : 'إضافة عنوان جديد',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: _isLoading ? null : _saveAddress,
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('حفظ'),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const Divider(),

          // Form
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // نوع العنوان والعنوان
                    Row(
                      children: [
                        Expanded(
                          child:
                              DropdownButtonFormField<user_model.AddressType>(
                                value: _selectedType,
                                decoration: const InputDecoration(
                                  labelText: 'نوع العنوان',
                                  prefixIcon: Icon(Icons.label),
                                ),
                                items: user_model.AddressType.values.map((
                                  type,
                                ) {
                                  return DropdownMenuItem(
                                    value: type,
                                    child: Text(_getTypeText(type)),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedType = value!;
                                  });
                                },
                              ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _titleController,
                            decoration: const InputDecoration(
                              labelText: 'اسم العنوان',
                              prefixIcon: Icon(Icons.title),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'اسم العنوان مطلوب';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // معلومات المستلم
                    Text(
                      'معلومات المستلم',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _firstNameController,
                            decoration: const InputDecoration(
                              labelText: 'الاسم الأول',
                              prefixIcon: Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الاسم الأول مطلوب';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _lastNameController,
                            decoration: const InputDecoration(
                              labelText: 'الاسم الأخير',
                              prefixIcon: Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الاسم الأخير مطلوب';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الهاتف',
                        prefixIcon: Icon(Icons.phone),
                      ),
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'رقم الهاتف مطلوب';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // العنوان
                    Text(
                      'العنوان',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _countryController,
                            decoration: const InputDecoration(
                              labelText: 'البلد',
                              prefixIcon: Icon(Icons.flag),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'البلد مطلوب';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _cityController,
                            decoration: const InputDecoration(
                              labelText: 'المدينة',
                              prefixIcon: Icon(Icons.location_city),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'المدينة مطلوبة';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _districtController,
                      decoration: const InputDecoration(
                        labelText: 'المنطقة/الحي',
                        prefixIcon: Icon(Icons.location_on),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'المنطقة مطلوبة';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _streetController,
                      decoration: const InputDecoration(
                        labelText: 'الشارع',
                        prefixIcon: Icon(Icons.streetview),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الشارع مطلوب';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _buildingController,
                            decoration: const InputDecoration(
                              labelText: 'رقم المبنى',
                              prefixIcon: Icon(Icons.business),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'رقم المبنى مطلوب';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _apartmentController,
                            decoration: const InputDecoration(
                              labelText: 'رقم الشقة (اختياري)',
                              prefixIcon: Icon(Icons.apartment),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _postalCodeController,
                      decoration: const InputDecoration(
                        labelText: 'الرمز البريدي (اختياري)',
                        prefixIcon: Icon(Icons.local_post_office),
                      ),
                      keyboardType: TextInputType.number,
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات إضافية (اختياري)',
                        prefixIcon: Icon(Icons.note),
                      ),
                      maxLines: 3,
                    ),

                    const SizedBox(height: 16),

                    // جعل العنوان افتراضي
                    CheckboxListTile(
                      title: const Text('جعل هذا العنوان افتراضي'),
                      subtitle: const Text(
                        'سيتم استخدامه كعنوان افتراضي للتوصيل',
                      ),
                      value: _isDefault,
                      onChanged: (value) {
                        setState(() {
                          _isDefault = value ?? false;
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTypeText(user_model.AddressType type) {
    switch (type) {
      case user_model.AddressType.home:
        return 'المنزل';
      case user_model.AddressType.work:
        return 'العمل';
      case user_model.AddressType.other:
        return 'أخرى';
    }
  }

  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final address = user_model.Address(
        id:
            widget.address?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text,
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        phone: _phoneController.text,
        country: _countryController.text,
        city: _cityController.text,
        district: _districtController.text,
        street: _streetController.text,
        buildingNumber: _buildingController.text,
        apartmentNumber: _apartmentController.text.isEmpty
            ? null
            : _apartmentController.text,
        postalCode: _postalCodeController.text.isEmpty
            ? null
            : _postalCodeController.text,
        additionalInfo: _notesController.text.isEmpty
            ? null
            : _notesController.text,
        isDefault: _isDefault,
        type: _selectedType,
      );

      widget.onSave(address);
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ العنوان: $e'),
          backgroundColor: AppColors.errorColor,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _phoneController.dispose();
    _countryController.dispose();
    _cityController.dispose();
    _districtController.dispose();
    _streetController.dispose();
    _buildingController.dispose();
    _apartmentController.dispose();
    _postalCodeController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
