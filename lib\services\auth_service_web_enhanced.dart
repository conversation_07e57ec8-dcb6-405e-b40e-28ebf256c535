import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة المحسنة للويب مع Google Sign-In
class AuthService {
  static GoogleSignIn? _googleSignIn;

  // تهيئة Google Sign-In حسب المنصة
  static GoogleSignIn get googleSignIn {
    if (_googleSignIn == null) {
      if (kIsWeb) {
        // إعداد خاص بالويب
        _googleSignIn = GoogleSignIn(
          clientId: '123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
          scopes: ['email', 'profile'],
        );
      } else {
        // إعداد للمنصات الأخرى
        _googleSignIn = GoogleSignIn(
          scopes: ['email', 'profile'],
        );
      }
    }
    return _googleSignIn!;
  }

  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google مع دعم الويب المحسن
  static Future<AuthResult> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('🔄 بدء عملية تسجيل الدخول بـ Google...');
        print('🌐 المنصة: ${kIsWeb ? "ويب" : "تطبيق"}');
      }

      // محاولة تسجيل الدخول
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ تم إلغاء تسجيل الدخول من قبل المستخدم');
        }
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      if (kDebugMode) {
        print('✅ تم اختيار حساب Google: ${googleUser.email}');
      }

      // الحصول على تفاصيل المصادقة
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (kDebugMode) {
        print('✅ تم الحصول على رموز المصادقة');
        print('📧 البريد الإلكتروني: ${googleUser.email}');
        print('👤 الاسم: ${googleUser.displayName}');
        print('🖼️ الصورة: ${googleUser.photoUrl}');
      }

      // إنشاء مستخدم في النظام المحاكي باستخدام البيانات الحقيقية
      final userData = user_model.User(
        id: 'google_${googleUser.id}',
        email: googleUser.email,
        firstName: googleUser.displayName?.split(' ').first ?? 'مستخدم',
        lastName: googleUser.displayName?.split(' ').skip(1).join(' ') ?? 'Google',
        phone: null,
        isEmailVerified: true, // حسابات Google محققة دائماً
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المستخدم في النظام المحاكي
      await FirebaseMockService.saveGoogleUser(userData, {
        'googleId': googleUser.id,
        'accessToken': googleAuth.accessToken,
        'idToken': googleAuth.idToken,
        'photoUrl': googleUser.photoUrl,
        'serverAuthCode': googleUser.serverAuthCode,
        'platform': kIsWeb ? 'web' : 'mobile',
      });

      if (kDebugMode) {
        print('✅ تم حفظ بيانات المستخدم في النظام المحاكي');
      }

      return AuthResult.success(userData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      }
      
      // في حالة فشل Google Sign-In، نقوم بمحاكاة تسجيل الدخول
      if (kIsWeb && e.toString().contains('popup_blocked_by_browser')) {
        return _simulateGoogleSignIn();
      }
      
      return AuthResult.failure('حدث خطأ في تسجيل الدخول بـ Google: $e');
    }
  }

  /// محاكاة تسجيل الدخول بـ Google في حالة فشل الطريقة الحقيقية
  static Future<AuthResult> _simulateGoogleSignIn() async {
    if (kDebugMode) {
      print('🔄 تفعيل محاكاة Google Sign-In...');
    }

    // إنشاء بيانات محاكاة واقعية
    final userData = user_model.User(
      id: 'google_demo_${DateTime.now().millisecondsSinceEpoch}',
      email: '<EMAIL>',
      firstName: 'مستخدم',
      lastName: 'تجريبي',
      phone: null,
      isEmailVerified: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // حفظ المستخدم في النظام المحاكي
    await FirebaseMockService.saveGoogleUser(userData, {
      'googleId': 'demo_google_id',
      'accessToken': 'demo_access_token',
      'idToken': 'demo_id_token',
      'photoUrl': null,
      'serverAuthCode': null,
      'platform': 'web_simulation',
      'note': 'هذا حساب تجريبي لأغراض الاختبار',
    });

    if (kDebugMode) {
      print('✅ تم إنشاء حساب Google تجريبي');
    }

    return AuthResult.success(userData);
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      // تسجيل الخروج من Google أيضاً
      await googleSignIn.signOut();
      
      // تسجيل الخروج من النظام المحاكي
      return await FirebaseMockService.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تسجيل الخروج: $e');
      }
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على معلومات حساب Google الحالي
  static GoogleSignInAccount? get currentGoogleUser => googleSignIn.currentUser;

  /// التحقق من تسجيل الدخول بـ Google
  static bool get isSignedInWithGoogle => googleSignIn.currentUser != null;
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
