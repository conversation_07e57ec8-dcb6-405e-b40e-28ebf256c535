import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';
import '../product/product_page.dart';

class WishlistPage extends StatefulWidget {
  const WishlistPage({super.key});

  @override
  State<WishlistPage> createState() => _WishlistPageState();
}

class _WishlistPageState extends State<WishlistPage> {
  final AppState _appState = AppState();

  @override
  void initState() {
    super.initState();
    _loadWishlist();
  }

  Future<void> _loadWishlist() async {
    final wishlist = await StorageService.loadWishlist();
    for (final item in wishlist) {
      _appState.addToWishlist(item.product);
    }
  }

  Future<void> _saveWishlist() async {
    await StorageService.saveWishlist(_appState.wishlistItems);
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _appState,
      builder: (context, child) {
        final wishlistItems = _appState.wishlistItems;

        return Scaffold(
          backgroundColor: AppColors.scaffoldBackground,
          appBar: AppBar(
            title: Text('المفضلة (${wishlistItems.length})'),
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.primaryText,
            elevation: 1,
            centerTitle: true,
            actions: [
              if (wishlistItems.isNotEmpty)
                TextButton(
                  onPressed: _clearWishlist,
                  child: const Text('مسح الكل'),
                ),
            ],
          ),
          body: wishlistItems.isEmpty
              ? _buildEmptyWishlist()
              : _buildWishlistContent(),
        );
      },
    );
  }

  Widget _buildEmptyWishlist() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.favorite_outline,
              size: 60,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد منتجات في المفضلة',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة المنتجات التي تعجبك إلى المفضلة',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('تصفح المنتجات'),
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistContent() {
    final wishlistItems = _appState.wishlistItems;

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      itemCount: wishlistItems.length,
      itemBuilder: (context, index) {
        return _buildWishlistItemCard(wishlistItems[index], index);
      },
    );
  }

  Widget _buildWishlistItemCard(WishlistItem wishlistItem, int index) {
    final product = wishlistItem.product;
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToProduct(wishlistItem),
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
          child: Row(
            children: [
              // صورة المنتج
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.scaffoldBackground,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.borderRadiusMedium,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    AppDimensions.borderRadiusMedium,
                  ),
                  child: Image.network(
                    product.images.isNotEmpty ? product.images.first : '',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.image_not_supported,
                        color: AppColors.grey,
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // معلومات المنتج
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.brand ?? '',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.secondaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          AppConstants.formatPrice(product.price),
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryColor,
                              ),
                        ),
                        if (product.originalPrice != null &&
                            product.originalPrice! > product.price) ...[
                          const SizedBox(width: 8),
                          Text(
                            AppConstants.formatPrice(product.originalPrice!),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  decoration: TextDecoration.lineThrough,
                                  color: AppColors.secondaryText,
                                ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // أزرار العمليات
              Column(
                children: [
                  IconButton(
                    onPressed: () => _removeFromWishlist(index),
                    icon: const Icon(
                      Icons.favorite,
                      color: AppColors.errorColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  IconButton(
                    onPressed: () => _addToCart(wishlistItem),
                    icon: const Icon(
                      Icons.shopping_cart_outlined,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToProduct(WishlistItem wishlistItem) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductPage(product: wishlistItem.product),
      ),
    );
  }

  void _removeFromWishlist(int index) {
    final wishlistItem = _appState.wishlistItems[index];
    final product = wishlistItem.product;
    _appState.removeFromWishlist(product.id);
    _saveWishlist();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف ${product.name} من المفضلة'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            _appState.addToWishlist(product);
            _saveWishlist();
          },
        ),
      ),
    );
  }

  void _addToCart(WishlistItem wishlistItem) {
    final product = wishlistItem.product;
    final cartItem = CartItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      product: product,
      quantity: 1,
      selectedColor: null,
      selectedSize: null,
    );

    _appState.addCartItem(cartItem);
    StorageService.saveCart(_appState.cartItems);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${product.name} إلى السلة'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _clearWishlist() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المفضلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _appState.clearWishlist();
              _saveWishlist();
              Navigator.of(context).pop();

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المنتجات من المفضلة'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}
