# دليل إعداد Google Sign-In الحقيقي

## 📋 الخطوات المطلوبة لإعداد Google Sign-In

### 1. إنشاء مشروع في Google Cloud Console

1. **اذهب إلى**: https://console.cloud.google.com/
2. **انقر على**: "Select a project" أو "إنشاء مشروع"
3. **أنشئ مشروع جديد**:
   - اسم المشروع: `VisionLens App`
   - معرف المشروع: `visionlens-app-[رقم عشوائي]`
4. **انقر على**: "إنشاء"

### 2. تفعيل Google+ API

1. **في لوحة التحكم**، اذهب إلى: "APIs & Services" > "Library"
2. **ابحث عن**: "Google+ API"
3. **انقر على**: "Google+ API"
4. **انقر على**: "تفعيل" (Enable)

### 3. إنشاء OAuth 2.0 Client ID

1. **اذهب إلى**: "APIs & Services" > "Credentials"
2. **انقر على**: "+ CREATE CREDENTIALS"
3. **اختر**: "OAuth 2.0 Client ID"

#### إعداد OAuth consent screen (إذا لم يكن معداً):
1. **انقر على**: "CONFIGURE CONSENT SCREEN"
2. **اختر**: "External" (للاختبار)
3. **املأ المعلومات المطلوبة**:
   - App name: `VisionLens`
   - User support email: بريدك الإلكتروني
   - Developer contact information: بريدك الإلكتروني
4. **انقر على**: "Save and Continue"
5. **في Scopes**: انقر "Save and Continue" (لا تحتاج تغيير شيء)
6. **في Test users**: أضف بريدك الإلكتروني
7. **انقر على**: "Save and Continue"

#### إنشاء Web Client ID:
1. **Application type**: اختر "Web application"
2. **Name**: `VisionLens Web Client`
3. **Authorized JavaScript origins**: أضف:
   - `http://localhost:55101`
   - `http://127.0.0.1:55101`
   - `http://localhost:3000` (للتطوير)
4. **Authorized redirect URIs**: أضف:
   - `http://localhost:55101/auth/callback`
   - `http://127.0.0.1:55101/auth/callback`
5. **انقر على**: "إنشاء"

### 4. نسخ Client ID

1. **بعد الإنشاء**، ستحصل على:
   - **Client ID**: شيء مثل `123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com`
   - **Client Secret**: احتفظ به آمناً
2. **انسخ Client ID**

### 5. تحديث الكود

#### في ملف `lib/services/auth_service_real_google.dart`:
```dart
// استبدل هذا السطر:
clientId: 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com',

// بـ Client ID الخاص بك:
clientId: '123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
```

#### في ملف `web/index.html`:
```html
<!-- استبدل هذا السطر: -->
<meta name="google-signin-client_id" content="123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com">

<!-- بـ Client ID الخاص بك: -->
<meta name="google-signin-client_id" content="YOUR_ACTUAL_CLIENT_ID.apps.googleusercontent.com">
```

### 6. إعداد للموبايل (Android) - اختياري

#### إنشاء Android Client ID:
1. **في نفس صفحة Credentials**، انقر "+ CREATE CREDENTIALS"
2. **اختر**: "OAuth 2.0 Client ID"
3. **Application type**: اختر "Android"
4. **Name**: `VisionLens Android Client`
5. **Package name**: `com.example.visionlens_app`
6. **SHA-1 certificate fingerprint**: احصل عليه من:
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```

### 7. اختبار الإعداد

1. **شغّل التطبيق**: `flutter run -d chrome`
2. **اذهب لصفحة تسجيل الدخول**
3. **انقر على**: "تسجيل الدخول بـ Google"
4. **يجب أن تظهر**: نافذة Google الحقيقية
5. **سجل دخول بحسابك الشخصي**

## 🔧 حل المشاكل الشائعة

### مشكلة "CLIENT_ID not set":
- تأكد من وضع Client ID الصحيح في الكود
- تأكد من تحديث ملف `index.html`

### مشكلة "popup_blocked_by_browser":
- اسمح بالنوافذ المنبثقة في المتصفح
- جرب متصفح آخر

### مشكلة "redirect_uri_mismatch":
- تأكد من إضافة عناوين URL الصحيحة في Google Console
- تأكد من استخدام نفس المنفذ (port)

### مشكلة "access_denied":
- تأكد من إضافة بريدك الإلكتروني في Test users
- تأكد من أن التطبيق في وضع Testing

## 📝 ملاحظات مهمة

1. **للتطوير**: يمكن استخدام وضع Testing
2. **للإنتاج**: ستحتاج لمراجعة Google للتطبيق
3. **الأمان**: لا تشارك Client Secret مع أحد
4. **النطاقات**: التطبيق يطلب فقط email و profile

## 🎯 النتيجة المتوقعة

بعد الإعداد الصحيح:
- ✅ ستظهر نافذة Google الحقيقية
- ✅ ستسجل دخول بحسابك الشخصي
- ✅ ستحصل على بياناتك الحقيقية (اسم، بريد، صورة)
- ✅ ستعمل المصادقة بشكل مثالي

---

**💡 نصيحة**: احتفظ بـ Client ID و Client Secret في مكان آمن ولا تضعهما في Git repository عام.
