// import 'package:json_annotation/json_annotation.dart';

// part 'product.g.dart';

// @JsonSerializable()
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String image;
  final List<String> images;
  final String categoryId;
  final String categoryName;
  final double rating;
  final int reviewsCount;
  final bool inStock;
  final int stockQuantity;
  final Map<String, dynamic> specifications;

  // خصائص خاصة بالنظارات والعدسات
  final String? brand;
  final ProductType type;
  final String? material;
  final String? color;
  final String? size;
  final String? power; // للعدسات
  final bool? requiresPrescription;
  final List<String>? availableColors;
  final List<String>? availableSizes;
  final List<String>? availablePowers;

  // خصائص إضافية للبحث والفلترة
  final List<String>? keywords;
  final int stock;

  // معلومات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isNew;
  final bool isFeatured;
  final bool isOnSale;
  final double? discountPercentage;
  final String? tags;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.image,
    required this.images,
    required this.categoryId,
    required this.categoryName,
    required this.rating,
    required this.reviewsCount,
    required this.inStock,
    required this.stockQuantity,
    required this.specifications,
    this.brand,
    required this.type,
    this.material,
    this.color,
    this.size,
    this.power,
    this.requiresPrescription,
    this.availableColors,
    this.availableSizes,
    this.availablePowers,
    this.keywords,
    this.stock = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isNew = false,
    this.isFeatured = false,
    this.isOnSale = false,
    this.discountPercentage,
    this.tags,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      image: json['image'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      categoryId: json['categoryId'] ?? '',
      categoryName: json['categoryName'] ?? '',
      rating: (json['rating'] ?? 0).toDouble(),
      reviewsCount: json['reviewsCount'] ?? 0,
      inStock: json['inStock'] ?? false,
      stockQuantity: json['stockQuantity'] ?? 0,
      specifications: Map<String, dynamic>.from(json['specifications'] ?? {}),
      brand: json['brand'],
      type: ProductType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => ProductType.eyeglasses,
      ),
      material: json['material'],
      color: json['color'],
      size: json['size'],
      power: json['power'],
      requiresPrescription: json['requiresPrescription'],
      availableColors: json['availableColors'] != null
          ? List<String>.from(json['availableColors'])
          : null,
      availableSizes: json['availableSizes'] != null
          ? List<String>.from(json['availableSizes'])
          : null,
      availablePowers: json['availablePowers'] != null
          ? List<String>.from(json['availablePowers'])
          : null,
      keywords: json['keywords'] != null
          ? List<String>.from(json['keywords'])
          : null,
      stock: json['stock'] ?? 0,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      isNew: json['isNew'] ?? false,
      isFeatured: json['isFeatured'] ?? false,
      isOnSale: json['isOnSale'] ?? false,
      discountPercentage: json['discountPercentage']?.toDouble(),
      tags: json['tags'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'originalPrice': originalPrice,
      'image': image,
      'images': images,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'rating': rating,
      'reviewsCount': reviewsCount,
      'inStock': inStock,
      'stockQuantity': stockQuantity,
      'specifications': specifications,
      'brand': brand,
      'type': type.toString().split('.').last,
      'material': material,
      'color': color,
      'size': size,
      'power': power,
      'requiresPrescription': requiresPrescription,
      'availableColors': availableColors,
      'availableSizes': availableSizes,
      'availablePowers': availablePowers,
      'keywords': keywords,
      'stock': stock,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isNew': isNew,
      'isFeatured': isFeatured,
      'isOnSale': isOnSale,
      'discountPercentage': discountPercentage,
      'tags': tags,
    };
  }

  // نسخة محدثة من المنتج
  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? image,
    List<String>? images,
    String? categoryId,
    String? categoryName,
    double? rating,
    int? reviewsCount,
    bool? inStock,
    int? stockQuantity,
    Map<String, dynamic>? specifications,
    String? brand,
    ProductType? type,
    String? material,
    String? color,
    String? size,
    String? power,
    bool? requiresPrescription,
    List<String>? availableColors,
    List<String>? availableSizes,
    List<String>? availablePowers,
    List<String>? keywords,
    int? stock,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isNew,
    bool? isFeatured,
    bool? isOnSale,
    double? discountPercentage,
    String? tags,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      image: image ?? this.image,
      images: images ?? this.images,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      rating: rating ?? this.rating,
      reviewsCount: reviewsCount ?? this.reviewsCount,
      inStock: inStock ?? this.inStock,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      specifications: specifications ?? this.specifications,
      brand: brand ?? this.brand,
      type: type ?? this.type,
      material: material ?? this.material,
      color: color ?? this.color,
      size: size ?? this.size,
      power: power ?? this.power,
      requiresPrescription: requiresPrescription ?? this.requiresPrescription,
      availableColors: availableColors ?? this.availableColors,
      availableSizes: availableSizes ?? this.availableSizes,
      availablePowers: availablePowers ?? this.availablePowers,
      keywords: keywords ?? this.keywords,
      stock: stock ?? this.stock,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isNew: isNew ?? this.isNew,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
    );
  }

  // حساب السعر النهائي بعد الخصم
  double get finalPrice {
    if (isOnSale && discountPercentage != null) {
      return price * (1 - discountPercentage! / 100);
    }
    return price;
  }

  // التحقق من وجود خصم
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  // نسبة الخصم
  double get calculatedDiscountPercentage {
    if (hasDiscount) {
      return ((originalPrice! - price) / originalPrice!) * 100;
    }
    return discountPercentage ?? 0;
  }

  // التحقق من توفر المنتج
  bool get isAvailable {
    return inStock && stockQuantity > 0;
  }

  // وصف نوع المنتج بالعربية
  String get typeDescription {
    switch (type) {
      case ProductType.eyeglasses:
        return 'نظارات طبية';
      case ProductType.sunglasses:
        return 'نظارات شمسية';
      case ProductType.contactLenses:
        return 'عدسات لاصقة';
      case ProductType.readingGlasses:
        return 'نظارات قراءة';
      case ProductType.accessories:
        return 'إكسسوارات';
      case ProductType.solutions:
        return 'محاليل العدسات';
    }
  }
}

// أنواع المنتجات
enum ProductType {
  eyeglasses,
  sunglasses,
  contactLenses,
  readingGlasses,
  accessories,
  solutions,
}

// مواصفات المنتج
class ProductSpecifications {
  final String? frameWidth;
  final String? lensWidth;
  final String? bridgeWidth;
  final String? templeLength;
  final String? lensHeight;
  final String? weight;
  final String? frameShape;
  final String? frameMaterial;
  final String? lensMaterial;
  final String? lensType;
  final String? uvProtection;
  final String? prescriptionRange;
  final String? baseCurve;
  final String? diameter;
  final String? waterContent;
  final String? wearingSchedule;
  final String? manufacturer;
  final String? countryOfOrigin;
  final String? warranty;

  const ProductSpecifications({
    this.frameWidth,
    this.lensWidth,
    this.bridgeWidth,
    this.templeLength,
    this.lensHeight,
    this.weight,
    this.frameShape,
    this.frameMaterial,
    this.lensMaterial,
    this.lensType,
    this.uvProtection,
    this.prescriptionRange,
    this.baseCurve,
    this.diameter,
    this.waterContent,
    this.wearingSchedule,
    this.manufacturer,
    this.countryOfOrigin,
    this.warranty,
  });

  factory ProductSpecifications.fromJson(Map<String, dynamic> json) {
    return ProductSpecifications(
      frameWidth: json['frameWidth'],
      lensWidth: json['lensWidth'],
      bridgeWidth: json['bridgeWidth'],
      templeLength: json['templeLength'],
      lensHeight: json['lensHeight'],
      weight: json['weight'],
      frameShape: json['frameShape'],
      frameMaterial: json['frameMaterial'],
      lensMaterial: json['lensMaterial'],
      lensType: json['lensType'],
      uvProtection: json['uvProtection'],
      prescriptionRange: json['prescriptionRange'],
      baseCurve: json['baseCurve'],
      diameter: json['diameter'],
      waterContent: json['waterContent'],
      wearingSchedule: json['wearingSchedule'],
      manufacturer: json['manufacturer'],
      countryOfOrigin: json['countryOfOrigin'],
      warranty: json['warranty'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frameWidth': frameWidth,
      'lensWidth': lensWidth,
      'bridgeWidth': bridgeWidth,
      'templeLength': templeLength,
      'lensHeight': lensHeight,
      'weight': weight,
      'frameShape': frameShape,
      'frameMaterial': frameMaterial,
      'lensMaterial': lensMaterial,
      'lensType': lensType,
      'uvProtection': uvProtection,
      'prescriptionRange': prescriptionRange,
      'baseCurve': baseCurve,
      'diameter': diameter,
      'waterContent': waterContent,
      'wearingSchedule': wearingSchedule,
      'manufacturer': manufacturer,
      'countryOfOrigin': countryOfOrigin,
      'warranty': warranty,
    };
  }
}
