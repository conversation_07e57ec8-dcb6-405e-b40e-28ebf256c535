import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../services/firestore_data_service.dart';
import '../../app_properties.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';
import '../main/main_page.dart';
import 'checkout_page.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final AppState _appState = AppState();

  @override
  void initState() {
    super.initState();
    _loadCartData();
  }

  Future<void> _loadCartData() async {
    try {
      // تحميل السلة من التخزين المحلي
      final savedCart = await StorageService.loadCart();

      // مسح السلة الحالية أولاً لتجنب التكرار
      _appState.clearCart();

      // إضافة العناصر المحفوظة
      for (final item in savedCart) {
        _appState.addToCart(
          item.product,
          color: item.selectedColor,
          size: item.selectedSize,
          quantity: item.quantity,
        );
      }

      // تحديث الواجهة
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل السلة: $e');
    }
  }

  Future<void> _saveCartData() async {
    await StorageService.saveCart(_appState.cartItems);
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _appState,
      builder: (context, child) {
        final cartItems = _appState.cartItems;

        return Scaffold(
          backgroundColor: AppColors.scaffoldBackground,
          appBar: AppBar(
            title: Text('السلة (${cartItems.length})'),
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.primaryText,
            elevation: 1,
            centerTitle: true,
            actions: [
              if (cartItems.isNotEmpty)
                TextButton(
                  onPressed: _clearCart,
                  child: const Text('مسح الكل'),
                ),
            ],
          ),
          body: cartItems.isEmpty ? _buildEmptyCart() : _buildCartContent(),
          bottomNavigationBar: cartItems.isNotEmpty
              ? _buildCheckoutBar()
              : null,
        );
      },
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 100,
            color: AppColors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            'السلة فارغة',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 12),
          Text(
            'أضف منتجات لتبدأ التسوق',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              // التنقل للصفحة الرئيسية مباشرة
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const MainPage(initialIndex: 0),
                ),
                (route) => false,
              );
            },
            child: const Text('تصفح المنتجات'),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    final cartItems = _appState.cartItems;

    return Column(
      children: [
        // عناصر السلة
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            itemCount: cartItems.length,
            itemBuilder: (context, index) {
              return _buildCartItemCard(cartItems[index], index);
            },
          ),
        ),

        // ملخص الطلب
        _buildOrderSummary(),
      ],
    );
  }

  Widget _buildCartItemCard(CartItem item, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.marginMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusMedium,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusMedium,
              ),
              child: item.product.image.isNotEmpty
                  ? Image.network(
                      item.product.image,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.visibility,
                          size: 30,
                          color: AppColors.grey,
                        );
                      },
                    )
                  : const Icon(
                      Icons.visibility,
                      size: 30,
                      color: AppColors.grey,
                    ),
            ),
          ),

          const SizedBox(width: 16),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (item.selectedColor != null || item.selectedSize != null)
                  Text(
                    '${item.selectedColor ?? ''} ${item.selectedSize ?? ''}'
                        .trim(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      AppConstants.formatPrice(item.product.price),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const Spacer(),
                    // أزرار الكمية
                    _buildQuantityControls(item, index),
                  ],
                ),
              ],
            ),
          ),

          // زر الحذف
          IconButton(
            onPressed: () => _removeItem(index),
            icon: const Icon(Icons.delete_outline, color: AppColors.errorColor),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControls(CartItem item, int index) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => _decreaseQuantity(index),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.remove,
              size: 16,
              color: AppColors.primaryText,
            ),
          ),
        ),
        Container(
          width: 40,
          alignment: Alignment.center,
          child: Text(
            item.quantity.toString(),
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
        ),
        GestureDetector(
          onTap: () => _increaseQuantity(index),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.add, size: 16, color: AppColors.white),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderSummary() {
    final subtotal = _appState.cartSubtotal;
    final shipping = _appState.cartShipping;
    final total = _appState.cartTotal;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
          topRight: Radius.circular(AppDimensions.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الفرعي',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              Text(
                AppConstants.formatPrice(subtotal),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الشحن', style: Theme.of(context).textTheme.bodyLarge),
              Text(
                shipping == 0 ? 'مجاني' : AppConstants.formatPrice(shipping),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: shipping == 0 ? AppColors.success : null,
                ),
              ),
            ],
          ),
          if (shipping == 0)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'شحن مجاني للطلبات أكثر من ${AppConstants.formatPrice(200000)}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.success),
              ),
            ),
          const Divider(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الكلي',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                AppConstants.formatPrice(total),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutBar() {
    final total = _appState.cartTotal;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // زر الشراء المباشر
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _directPurchase,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: AppColors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.flash_on, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'شراء مباشر (${AppConstants.formatPrice(total)})',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            // زر متابعة الدفع العادي
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _proceedToCheckout,
                child: Text(
                  'متابعة الدفع (${AppConstants.formatPrice(total)})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _increaseQuantity(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      _appState.updateCartItemQuantity(item.id, item.quantity + 1);
      _saveCartData();
    }
  }

  void _decreaseQuantity(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      if (item.quantity > 1) {
        _appState.updateCartItemQuantity(item.id, item.quantity - 1);
        _saveCartData();
      }
    }
  }

  void _removeItem(int index) {
    final cartItems = _appState.cartItems;
    if (index < cartItems.length) {
      final item = cartItems[index];
      _appState.removeFromCart(item.id);
      _saveCartData();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف المنتج من السلة'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _clearCart() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح السلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _appState.clearCart();
              _saveCartData();
              Navigator.of(context).pop();
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _proceedToCheckout() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CheckoutPage()),
    );
  }

  void _directPurchase() {
    // الشراء المباشر بالدفع عند الاستلام
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('شراء مباشر'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم تأكيد طلبك بالدفع عند الاستلام'),
            SizedBox(height: 8),
            Text('• سيتم التواصل معك خلال 24 ساعة'),
            Text('• الدفع عند استلام الطلب'),
            Text('• إمكانية الإرجاع خلال 7 أيام'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmDirectPurchase();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: AppColors.white,
            ),
            child: const Text('تأكيد الطلب'),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDirectPurchase() async {
    try {
      // إنشاء طلب مباشر بالدفع عند الاستلام
      final currentUser = _appState.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى تسجيل الدخول أولاً'),
            backgroundColor: AppColors.errorColor,
          ),
        );
        return;
      }

      final cartItems = _appState.cartItems;
      final subtotal = cartItems.fold<double>(
        0,
        (sum, item) => sum + (item.product.finalPrice * item.quantity),
      );
      const shippingCost = 5000.0;
      final total = subtotal + shippingCost;

      final orderId = DateTime.now().millisecondsSinceEpoch.toString();
      final orderData = {
        'id': orderId,
        'userId': currentUser.id,
        'customerName': '${currentUser.firstName} ${currentUser.lastName}',
        'customerEmail': currentUser.email,
        'customerPhone': currentUser.phone ?? '',
        'items': cartItems
            .map(
              (item) => {
                'productId': item.product.id,
                'productName': item.product.name,
                'quantity': item.quantity,
                'price': item.product.finalPrice,
                'total': item.product.finalPrice * item.quantity,
              },
            )
            .toList(),
        'subtotal': subtotal,
        'shippingCost': shippingCost,
        'total': total,
        'currency': 'IQD',
        'status': 'pending',
        'paymentMethod': 'cash_on_delivery',
        'shippingMethod': 'standard',
        'address': 'سيتم التواصل لتحديد العنوان',
        'notes': 'طلب شراء مباشر - دفع عند الاستلام',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // حفظ الطلب في Firestore
      await FirestoreDataService.addOrder(orderData);

      // مسح السلة
      _appState.clearCart();
      await StorageService.saveCart([]);

      // عرض رسالة نجاح
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('تم تأكيد الطلب!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text('رقم الطلب: $orderId'),
                const SizedBox(height: 8),
                const Text('سيتم التواصل معك خلال 24 ساعة لتأكيد التفاصيل'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // إغلاق الحوار
                  Navigator.of(context).pop(); // العودة للصفحة السابقة
                },
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تأكيد الطلب: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    }
  }
}
