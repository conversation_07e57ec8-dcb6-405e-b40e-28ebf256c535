# 🚀 تشغيل Google Sign-In الحقيقي - دليل سريع

## ⚡ الخطوات السريعة

### 1. إنشاء Google Cloud Project
1. اذهب إلى: https://console.cloud.google.com/
2. أنشئ مشروع جديد: `VisionLens App`
3. فعّل Google+ API

### 2. إنشاء OAuth Client ID
1. اذهب إلى: APIs & Services > Credentials
2. أنشئ OAuth 2.0 Client ID
3. اختر: Web application
4. أضف Authorized JavaScript origins:
   - `http://localhost:55101`
   - `http://127.0.0.1:55101`

### 3. نسخ Client ID
انسخ Client ID الذي يبدو مثل:
```
123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
```

### 4. تحديث الكود

#### في `lib/services/auth_service_real_google.dart` السطر 18:
```dart
clientId: 'ضع_CLIENT_ID_هنا.apps.googleusercontent.com',
```

#### في `web/index.html` السطر 26:
```html
<meta name="google-signin-client_id" content="ضع_CLIENT_ID_هنا.apps.googleusercontent.com">
```

### 5. تشغيل التطبيق
```bash
flutter run -d chrome
```

### 6. اختبار Google Sign-In
1. اذهب لصفحة تسجيل الدخول
2. انقر "تسجيل الدخول بـ Google"
3. سجل دخول بحسابك الشخصي

## 🎯 النتيجة المتوقعة
- ✅ نافذة Google الحقيقية
- ✅ تسجيل دخول بحسابك الشخصي
- ✅ عرض بياناتك الحقيقية (اسم، بريد، صورة)

## 🔧 حل المشاكل
- **CLIENT_ID not set**: تأكد من وضع Client ID الصحيح
- **popup_blocked**: اسمح بالنوافذ المنبثقة
- **redirect_uri_mismatch**: تأكد من إضافة URLs الصحيحة

---
📖 للتعليمات المفصلة، راجع: `GOOGLE_SETUP_GUIDE.md`
