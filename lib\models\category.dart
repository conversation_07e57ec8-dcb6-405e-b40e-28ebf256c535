import 'package:flutter/material.dart';
// import 'package:json_annotation/json_annotation.dart';

// part 'category.g.dart';

// @JsonSerializable()
class Category {
  final String id;
  final String name;
  final String description;
  final String image;
  final String icon;
  final Color primaryColor;
  final Color secondaryColor;
  final String? parentId;
  final List<Category> subcategories;
  final int productCount;
  final bool isActive;
  final bool isFeatured;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Category({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    this.parentId,
    this.subcategories = const [],
    this.productCount = 0,
    this.isActive = true,
    this.isFeatured = false,
    this.sortOrder = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      image: json['image'] ?? '',
      icon: json['icon'] ?? '',
      primaryColor: _colorFromJson(json['primaryColor'] ?? '#2196F3'),
      secondaryColor: _colorFromJson(json['secondaryColor'] ?? '#BBDEFB'),
      parentId: json['parentId'],
      subcategories:
          (json['subcategories'] as List<dynamic>?)
              ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      productCount: json['productCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      isFeatured: json['isFeatured'] ?? false,
      sortOrder: json['sortOrder'] ?? 0,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image': image,
      'icon': icon,
      'primaryColor': _colorToJson(primaryColor),
      'secondaryColor': _colorToJson(secondaryColor),
      'parentId': parentId,
      'subcategories': subcategories.map((e) => e.toJson()).toList(),
      'productCount': productCount,
      'isActive': isActive,
      'isFeatured': isFeatured,
      'sortOrder': sortOrder,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // التحقق من وجود فئات فرعية
  bool get hasSubcategories => subcategories.isNotEmpty;

  // التحقق من كونها فئة رئيسية
  bool get isMainCategory => parentId == null;

  // نسخة محدثة من الفئة
  Category copyWith({
    String? id,
    String? name,
    String? description,
    String? image,
    String? icon,
    Color? primaryColor,
    Color? secondaryColor,
    String? parentId,
    List<Category>? subcategories,
    int? productCount,
    bool? isActive,
    bool? isFeatured,
    int? sortOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      icon: icon ?? this.icon,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      parentId: parentId ?? this.parentId,
      subcategories: subcategories ?? this.subcategories,
      productCount: productCount ?? this.productCount,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // تحويل اللون من JSON
  static Color _colorFromJson(String colorString) {
    return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
  }

  // تحويل اللون إلى JSON
  static String _colorToJson(Color color) {
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
  }
}

// فئات النظارات والعدسات المحددة مسبقاً
class PredefinedCategories {
  static const List<Map<String, dynamic>> categories = [
    {
      'id': 'eyeglasses',
      'name': 'نظارات طبية',
      'description': 'نظارات طبية بأحدث التصاميم والتقنيات',
      'image': 'assets/images/categories/eyeglasses.png',
      'icon': 'assets/icons/eyeglasses.svg',
      'primaryColor': '#1976D2',
      'secondaryColor': '#BBDEFB',
      'subcategories': [
        {
          'id': 'men_eyeglasses',
          'name': 'نظارات رجالية',
          'description': 'نظارات طبية للرجال',
          'image': 'assets/images/categories/men_eyeglasses.png',
          'icon': 'assets/icons/men_eyeglasses.svg',
          'primaryColor': '#1976D2',
          'secondaryColor': '#BBDEFB',
        },
        {
          'id': 'women_eyeglasses',
          'name': 'نظارات نسائية',
          'description': 'نظارات طبية للنساء',
          'image': 'assets/images/categories/women_eyeglasses.png',
          'icon': 'assets/icons/women_eyeglasses.svg',
          'primaryColor': '#E91E63',
          'secondaryColor': '#F8BBD9',
        },
        {
          'id': 'kids_eyeglasses',
          'name': 'نظارات أطفال',
          'description': 'نظارات طبية للأطفال',
          'image': 'assets/images/categories/kids_eyeglasses.png',
          'icon': 'assets/icons/kids_eyeglasses.svg',
          'primaryColor': '#FF9800',
          'secondaryColor': '#FFE0B2',
        },
      ],
    },
    {
      'id': 'sunglasses',
      'name': 'نظارات شمسية',
      'description': 'نظارات شمسية أنيقة وعملية',
      'image': 'assets/images/categories/sunglasses.png',
      'icon': 'assets/icons/sunglasses.svg',
      'primaryColor': '#FF9800',
      'secondaryColor': '#FFE0B2',
      'subcategories': [
        {
          'id': 'men_sunglasses',
          'name': 'نظارات شمسية رجالية',
          'description': 'نظارات شمسية للرجال',
          'image': 'assets/images/categories/men_sunglasses.png',
          'icon': 'assets/icons/men_sunglasses.svg',
          'primaryColor': '#424242',
          'secondaryColor': '#E0E0E0',
        },
        {
          'id': 'women_sunglasses',
          'name': 'نظارات شمسية نسائية',
          'description': 'نظارات شمسية للنساء',
          'image': 'assets/images/categories/women_sunglasses.png',
          'icon': 'assets/icons/women_sunglasses.svg',
          'primaryColor': '#E91E63',
          'secondaryColor': '#F8BBD9',
        },
        {
          'id': 'sports_sunglasses',
          'name': 'نظارات رياضية',
          'description': 'نظارات شمسية للرياضة',
          'image': 'assets/images/categories/sports_sunglasses.png',
          'icon': 'assets/icons/sports_sunglasses.svg',
          'primaryColor': '#4CAF50',
          'secondaryColor': '#C8E6C9',
        },
      ],
    },
    {
      'id': 'contact_lenses',
      'name': 'عدسات لاصقة',
      'description': 'عدسات لاصقة بجودة عالية',
      'image': 'assets/images/categories/contact_lenses.png',
      'icon': 'assets/icons/contact_lenses.svg',
      'primaryColor': '#2196F3',
      'secondaryColor': '#BBDEFB',
      'subcategories': [
        {
          'id': 'daily_lenses',
          'name': 'عدسات يومية',
          'description': 'عدسات لاصقة يومية',
          'image': 'assets/images/categories/daily_lenses.png',
          'icon': 'assets/icons/daily_lenses.svg',
          'primaryColor': '#4CAF50',
          'secondaryColor': '#C8E6C9',
        },
        {
          'id': 'monthly_lenses',
          'name': 'عدسات شهرية',
          'description': 'عدسات لاصقة شهرية',
          'image': 'assets/images/categories/monthly_lenses.png',
          'icon': 'assets/icons/monthly_lenses.svg',
          'primaryColor': '#FF9800',
          'secondaryColor': '#FFE0B2',
        },
        {
          'id': 'colored_lenses',
          'name': 'عدسات ملونة',
          'description': 'عدسات لاصقة ملونة',
          'image': 'assets/images/categories/colored_lenses.png',
          'icon': 'assets/icons/colored_lenses.svg',
          'primaryColor': '#9C27B0',
          'secondaryColor': '#E1BEE7',
        },
      ],
    },
    {
      'id': 'reading_glasses',
      'name': 'نظارات قراءة',
      'description': 'نظارات قراءة مريحة وأنيقة',
      'image': 'assets/images/categories/reading_glasses.png',
      'icon': 'assets/icons/reading_glasses.svg',
      'primaryColor': '#795548',
      'secondaryColor': '#D7CCC8',
    },
    {
      'id': 'accessories',
      'name': 'إكسسوارات',
      'description': 'إكسسوارات النظارات والعدسات',
      'image': 'assets/images/categories/accessories.png',
      'icon': 'assets/icons/accessories.svg',
      'primaryColor': '#607D8B',
      'secondaryColor': '#CFD8DC',
      'subcategories': [
        {
          'id': 'cases',
          'name': 'علب النظارات',
          'description': 'علب حفظ النظارات',
          'image': 'assets/images/categories/cases.png',
          'icon': 'assets/icons/cases.svg',
          'primaryColor': '#795548',
          'secondaryColor': '#D7CCC8',
        },
        {
          'id': 'chains',
          'name': 'سلاسل النظارات',
          'description': 'سلاسل وحبال النظارات',
          'image': 'assets/images/categories/chains.png',
          'icon': 'assets/icons/chains.svg',
          'primaryColor': '#FF9800',
          'secondaryColor': '#FFE0B2',
        },
        {
          'id': 'cleaning',
          'name': 'أدوات التنظيف',
          'description': 'أدوات تنظيف النظارات',
          'image': 'assets/images/categories/cleaning.png',
          'icon': 'assets/icons/cleaning.svg',
          'primaryColor': '#2196F3',
          'secondaryColor': '#BBDEFB',
        },
      ],
    },
    {
      'id': 'solutions',
      'name': 'محاليل العدسات',
      'description': 'محاليل تنظيف وحفظ العدسات',
      'image': 'assets/images/categories/solutions.png',
      'icon': 'assets/icons/solutions.svg',
      'primaryColor': '#00BCD4',
      'secondaryColor': '#B2EBF2',
      'subcategories': [
        {
          'id': 'cleaning_solutions',
          'name': 'محاليل التنظيف',
          'description': 'محاليل تنظيف العدسات',
          'image': 'assets/images/categories/cleaning_solutions.png',
          'icon': 'assets/icons/cleaning_solutions.svg',
          'primaryColor': '#00BCD4',
          'secondaryColor': '#B2EBF2',
        },
        {
          'id': 'storage_solutions',
          'name': 'محاليل الحفظ',
          'description': 'محاليل حفظ العدسات',
          'image': 'assets/images/categories/storage_solutions.png',
          'icon': 'assets/icons/storage_solutions.svg',
          'primaryColor': '#4CAF50',
          'secondaryColor': '#C8E6C9',
        },
      ],
    },
  ];

  // تحويل البيانات المحددة مسبقاً إلى كائنات Category
  static List<Category> getCategories() {
    return categories.map((categoryData) {
      final subcategories =
          (categoryData['subcategories'] as List<dynamic>?)
              ?.map(
                (subData) => Category.fromJson(subData as Map<String, dynamic>),
              )
              .toList() ??
          [];

      return Category(
        id: categoryData['id'],
        name: categoryData['name'],
        description: categoryData['description'],
        image: categoryData['image'],
        icon: categoryData['icon'],
        primaryColor: Color(
          int.parse(categoryData['primaryColor'].replaceFirst('#', '0xFF')),
        ),
        secondaryColor: Color(
          int.parse(categoryData['secondaryColor'].replaceFirst('#', '0xFF')),
        ),
        subcategories: subcategories,
        productCount: 0,
        isActive: true,
        isFeatured: true,
        sortOrder: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }).toList();
  }
}
