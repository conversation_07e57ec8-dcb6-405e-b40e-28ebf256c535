import 'package:flutter/material.dart';

class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final String? actionUrl;
  final Map<String, dynamic>? data;
  final String? imageUrl;

  const AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.actionUrl,
    this.data,
    this.imageUrl,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => NotificationType.general,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      isRead: json['isRead'] ?? false,
      actionUrl: json['actionUrl'],
      data: json['data'],
      imageUrl: json['imageUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'actionUrl': actionUrl,
      'data': data,
      'imageUrl': imageUrl,
    };
  }

  AppNotification copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    DateTime? createdAt,
    bool? isRead,
    String? actionUrl,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      actionUrl: actionUrl ?? this.actionUrl,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  // الحصول على أيقونة الإشعار
  IconData get icon {
    switch (type) {
      case NotificationType.orderUpdate:
        return Icons.shopping_bag;
      case NotificationType.promotion:
        return Icons.local_offer;
      case NotificationType.newProduct:
        return Icons.new_releases;
      case NotificationType.reminder:
        return Icons.schedule;
      case NotificationType.welcome:
        return Icons.waving_hand;
      case NotificationType.system:
        return Icons.settings;
      case NotificationType.general:
      default:
        return Icons.notifications;
    }
  }

  // الحصول على لون الإشعار
  Color get color {
    switch (type) {
      case NotificationType.orderUpdate:
        return Colors.blue;
      case NotificationType.promotion:
        return Colors.orange;
      case NotificationType.newProduct:
        return Colors.green;
      case NotificationType.reminder:
        return Colors.purple;
      case NotificationType.welcome:
        return Colors.pink;
      case NotificationType.system:
        return Colors.grey;
      case NotificationType.general:
      default:
        return Colors.blue;
    }
  }

  // الحصول على وصف نوع الإشعار
  String get typeDescription {
    switch (type) {
      case NotificationType.orderUpdate:
        return 'تحديث الطلب';
      case NotificationType.promotion:
        return 'عرض خاص';
      case NotificationType.newProduct:
        return 'منتج جديد';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.welcome:
        return 'ترحيب';
      case NotificationType.system:
        return 'النظام';
      case NotificationType.general:
      default:
        return 'عام';
    }
  }

  // تنسيق وقت الإنشاء
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  // التحقق من كون الإشعار جديد (أقل من ساعة)
  bool get isNew {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 1;
  }

  // التحقق من أهمية الإشعار
  bool get isImportant {
    return type == NotificationType.orderUpdate || 
           type == NotificationType.reminder ||
           type == NotificationType.system;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, isRead: $isRead)';
  }
}

enum NotificationType {
  general,      // عام
  orderUpdate,  // تحديث الطلب
  promotion,    // عرض خاص
  newProduct,   // منتج جديد
  reminder,     // تذكير
  welcome,      // ترحيب
  system,       // النظام
}

// إعدادات الإشعارات
class NotificationSettings {
  final bool enablePushNotifications;
  final bool enableOrderUpdates;
  final bool enablePromotions;
  final bool enableNewProducts;
  final bool enableReminders;
  final bool enableSound;
  final bool enableVibration;
  final String soundType;
  final List<String> mutedCategories;

  const NotificationSettings({
    this.enablePushNotifications = true,
    this.enableOrderUpdates = true,
    this.enablePromotions = true,
    this.enableNewProducts = true,
    this.enableReminders = true,
    this.enableSound = true,
    this.enableVibration = true,
    this.soundType = 'default',
    this.mutedCategories = const [],
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      enablePushNotifications: json['enablePushNotifications'] ?? true,
      enableOrderUpdates: json['enableOrderUpdates'] ?? true,
      enablePromotions: json['enablePromotions'] ?? true,
      enableNewProducts: json['enableNewProducts'] ?? true,
      enableReminders: json['enableReminders'] ?? true,
      enableSound: json['enableSound'] ?? true,
      enableVibration: json['enableVibration'] ?? true,
      soundType: json['soundType'] ?? 'default',
      mutedCategories: List<String>.from(json['mutedCategories'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enablePushNotifications': enablePushNotifications,
      'enableOrderUpdates': enableOrderUpdates,
      'enablePromotions': enablePromotions,
      'enableNewProducts': enableNewProducts,
      'enableReminders': enableReminders,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'soundType': soundType,
      'mutedCategories': mutedCategories,
    };
  }

  NotificationSettings copyWith({
    bool? enablePushNotifications,
    bool? enableOrderUpdates,
    bool? enablePromotions,
    bool? enableNewProducts,
    bool? enableReminders,
    bool? enableSound,
    bool? enableVibration,
    String? soundType,
    List<String>? mutedCategories,
  }) {
    return NotificationSettings(
      enablePushNotifications: enablePushNotifications ?? this.enablePushNotifications,
      enableOrderUpdates: enableOrderUpdates ?? this.enableOrderUpdates,
      enablePromotions: enablePromotions ?? this.enablePromotions,
      enableNewProducts: enableNewProducts ?? this.enableNewProducts,
      enableReminders: enableReminders ?? this.enableReminders,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      soundType: soundType ?? this.soundType,
      mutedCategories: mutedCategories ?? this.mutedCategories,
    );
  }

  // التحقق من تمكين نوع إشعار معين
  bool isTypeEnabled(NotificationType type) {
    if (!enablePushNotifications) return false;
    
    switch (type) {
      case NotificationType.orderUpdate:
        return enableOrderUpdates;
      case NotificationType.promotion:
        return enablePromotions;
      case NotificationType.newProduct:
        return enableNewProducts;
      case NotificationType.reminder:
        return enableReminders;
      case NotificationType.welcome:
      case NotificationType.system:
      case NotificationType.general:
      default:
        return true;
    }
  }
}
