import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_simple.dart' as user_model;
import 'firebase_mock_service.dart';

/// خدمة المصادقة مع Google Sign-In الحقيقي فقط
class AuthService {
  static GoogleSignIn? _googleSignIn;

  // إعداد Google Sign-In الحقيقي
  static GoogleSignIn get googleSignIn {
    if (_googleSignIn == null) {
      if (kIsWeb) {
        // إعداد للويب - يحتاج Client ID صحيح
        _googleSignIn = GoogleSignIn(
          clientId:
              '627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com', // Client ID الحقيقي
          scopes: ['email', 'profile'],
          // إعدادات إضافية للويب
        );
      } else {
        // إعداد للموبايل
        _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);
      }
    }
    return _googleSignIn!;
  }

  // الحصول على المستخدم الحالي
  static user_model.User? get currentUser =>
      FirebaseMockService.getCurrentUser();

  // التحقق من حالة تسجيل الدخول
  static bool get isLoggedIn => FirebaseMockService.isLoggedIn;

  // ==================== تسجيل الدخول ====================

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await FirebaseMockService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الدخول بـ Google الحقيقي بحسابك الشخصي
  static Future<AuthResult> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('🔄 بدء تسجيل الدخول بـ Google الحقيقي...');
        print('🌐 المنصة: ${kIsWeb ? "ويب" : "موبايل"}');
      }

      // محاولة تسجيل الدخول الصامت أولاً
      GoogleSignInAccount? googleUser = await googleSignIn.signInSilently();

      if (googleUser == null) {
        if (kDebugMode) {
          print('🔄 تسجيل الدخول الصامت فشل، محاولة تسجيل الدخول التفاعلي...');
        }

        // إذا فشل التسجيل الصامت، جرب التسجيل التفاعلي
        googleUser = await googleSignIn.signIn();
      }

      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ تم إلغاء تسجيل الدخول من قبل المستخدم');
        }
        return AuthResult.failure('تم إلغاء تسجيل الدخول');
      }

      if (kDebugMode) {
        print('✅ تم تسجيل الدخول بنجاح!');
        print('📧 البريد الإلكتروني: ${googleUser.email}');
        print('👤 الاسم: ${googleUser.displayName}');
        print('🆔 Google ID: ${googleUser.id}');
        print('🖼️ صورة الملف الشخصي: ${googleUser.photoUrl}');
      }

      // الحصول على تفاصيل المصادقة (بدون People API)
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (kDebugMode) {
        print('🔑 تم الحصول على رموز المصادقة');
        print(
          '🎫 Access Token: ${googleAuth.accessToken != null ? "متوفر" : "غير متوفر"}',
        );
        print(
          '🎟️ ID Token: ${googleAuth.idToken != null ? "متوفر" : "غير متوفر"}',
        );
      }

      // إنشاء مستخدم في النظام المحاكي باستخدام بياناتك الحقيقية
      final userData = user_model.User(
        id: 'google_${googleUser.id}',
        email: googleUser.email,
        firstName: googleUser.displayName?.split(' ').first ?? 'مستخدم',
        lastName: googleUser.displayName?.split(' ').skip(1).join(' ') ?? '',
        phone: null,
        isEmailVerified: true, // حسابات Google محققة دائماً
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ بياناتك الحقيقية في النظام المحاكي
      await FirebaseMockService.saveGoogleUser(userData, {
        'googleId': googleUser.id,
        'accessToken': googleAuth.accessToken,
        'idToken': googleAuth.idToken,
        'photoUrl': googleUser.photoUrl,
        'serverAuthCode': googleUser.serverAuthCode,
        'platform': kIsWeb ? 'web' : 'mobile',
        'signInMethod': 'google_real',
        'signInTime': DateTime.now().toIso8601String(),
        'userAgent': kIsWeb ? 'web_browser' : 'mobile_app',
      });

      if (kDebugMode) {
        print('✅ تم حفظ بياناتك في النظام المحاكي');
        print('🎉 مرحباً ${userData.firstName}!');
      }

      return AuthResult.success(userData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      }

      // رسائل خطأ مفصلة
      String errorMessage = 'حدث خطأ في تسجيل الدخول بـ Google';

      if (e.toString().contains('CLIENT_ID')) {
        errorMessage =
            'يجب إعداد Google Client ID أولاً. راجع التعليمات في الكود.';
      } else if (e.toString().contains('popup_blocked')) {
        errorMessage =
            'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.';
      } else if (e.toString().contains('popup_closed')) {
        errorMessage =
            'تم إغلاق نافذة تسجيل الدخول. يرجى المحاولة مرة أخرى والانتظار حتى اكتمال العملية.';
      } else if (e.toString().contains('network')) {
        errorMessage =
            'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وأعد المحاولة.';
      } else if (e.toString().contains('access_denied')) {
        errorMessage = 'تم رفض الوصول. تأكد من الموافقة على الأذونات المطلوبة.';
      } else if (e.toString().contains('invalid_request')) {
        errorMessage = 'طلب غير صالح. تحقق من إعدادات Google OAuth.';
      }

      return AuthResult.failure(errorMessage);
    }
  }

  /// إنشاء حساب جديد
  static Future<AuthResult> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final result = await FirebaseMockService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
      );

      if (result.isSuccess) {
        return AuthResult.success(result.user);
      } else {
        return AuthResult.failure(result.errorMessage ?? 'فشل في إنشاء الحساب');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ غير متوقع: $e');
    }
  }

  /// تسجيل الخروج
  static Future<bool> signOut() async {
    try {
      if (kDebugMode) {
        print('🔄 تسجيل الخروج...');
      }

      // تسجيل الخروج من Google
      await googleSignIn.signOut();

      // تسجيل الخروج من النظام المحاكي
      final result = await FirebaseMockService.signOut();

      if (kDebugMode) {
        print('✅ تم تسجيل الخروج بنجاح');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الخروج: $e');
      }
      return false;
    }
  }

  /// إرسال رسالة تأكيد البريد الإلكتروني (محاكاة)
  static Future<bool> sendEmailVerification() async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// إعادة تعيين كلمة المرور (محاكاة)
  static Future<bool> sendPasswordResetEmail(String email) async {
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }

  /// الحصول على معلومات حساب Google الحالي
  static GoogleSignInAccount? get currentGoogleUser => googleSignIn.currentUser;

  /// التحقق من تسجيل الدخول بـ Google
  static bool get isSignedInWithGoogle => googleSignIn.currentUser != null;

  /// الحصول على معلومات مفصلة عن المستخدم الحالي
  static Map<String, dynamic>? get currentUserDetails {
    final user = currentUser;
    final googleUser = currentGoogleUser;

    if (user == null) return null;

    return {
      'user': user,
      'googleUser': googleUser,
      'isGoogleSignIn': googleUser != null,
      'email': user.email,
      'name': '${user.firstName} ${user.lastName}',
      'photoUrl': googleUser?.photoUrl,
      'isEmailVerified': user.isEmailVerified,
      'signInTime': user.updatedAt,
    };
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final user_model.User? user;

  AuthResult.success(this.user) : isSuccess = true, errorMessage = null;
  AuthResult.failure(this.errorMessage) : isSuccess = false, user = null;
}
