import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../services/search_service.dart';
import '../../models/category.dart';

class FiltersPage extends StatefulWidget {
  final SearchFilters initialFilters;
  final Function(SearchFilters) onFiltersApplied;

  const FiltersPage({
    super.key,
    required this.initialFilters,
    required this.onFiltersApplied,
  });

  @override
  State<FiltersPage> createState() => _FiltersPageState();
}

class _FiltersPageState extends State<FiltersPage> {
  late SearchFilters _filters;

  // قوائم البيانات
  final List<String> _brands = [
    'Ray-Ban',
    '<PERSON>ley',
    'Gucci',
    '<PERSON>rada',
    'Versace',
    'Tom Ford',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Costa Del Mar',
    'Warby Parker',
  ];

  final List<Category> _categories = [
    Category(
      id: 'sunglasses',
      name: 'نظارات شمسية',
      icon: 'sunglasses',
      description: 'نظارات شمسية عصرية',
      image: 'assets/images/categories/sunglasses.png',
      primaryColor: Colors.orange,
      secondaryColor: Colors.orange.shade100,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      id: 'eyeglasses',
      name: 'نظارات طبية',
      icon: 'eyeglasses',
      description: 'نظارات طبية متنوعة',
      image: 'assets/images/categories/eyeglasses.png',
      primaryColor: Colors.blue,
      secondaryColor: Colors.blue.shade100,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      id: 'contact-lenses',
      name: 'عدسات لاصقة',
      icon: 'contact_lens',
      description: 'عدسات لاصقة مريحة',
      image: 'assets/images/categories/contact_lenses.png',
      primaryColor: Colors.green,
      secondaryColor: Colors.green.shade100,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _filters = widget.initialFilters;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('الفلاتر'),
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.primaryText,
        elevation: 1,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _clearAllFilters,
            child: const Text('مسح الكل'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildCategoryFilter(),
            _buildBrandFilter(),
            _buildPriceFilter(),
            _buildRatingFilter(),
            _buildAvailabilityFilter(),
            _buildSortOptions(),
            const SizedBox(height: 100), // مساحة للأزرار السفلية
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفئة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _categories.map((category) {
              final isSelected = _filters.categoryId == category.id;
              return FilterChip(
                label: Text(category.name),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _filters = _filters.copyWith(
                      categoryId: selected ? category.id : null,
                    );
                  });
                },
                selectedColor: AppColors.primaryColor.withValues(alpha: 0.2),
                checkmarkColor: AppColors.primaryColor,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBrandFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'العلامة التجارية',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _brands.map((brand) {
              final isSelected = _filters.brand == brand;
              return FilterChip(
                label: Text(brand),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    _filters = _filters.copyWith(
                      brand: selected ? brand : null,
                    );
                  });
                },
                selectedColor: AppColors.primaryColor.withValues(alpha: 0.2),
                checkmarkColor: AppColors.primaryColor,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceFilter() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نطاق السعر',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الحد الأدنى',
                    prefixText: 'د.ع ',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  initialValue: _filters.minPrice?.toString() ?? '',
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    setState(() {
                      _filters = _filters.copyWith(minPrice: price);
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الحد الأقصى',
                    prefixText: 'د.ع ',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  initialValue: _filters.maxPrice?.toString() ?? '',
                  onChanged: (value) {
                    final price = double.tryParse(value);
                    setState(() {
                      _filters = _filters.copyWith(maxPrice: price);
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // نطاقات سعر سريعة
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildPriceRangeChip('أقل من 50,000', null, 50000),
              _buildPriceRangeChip('50,000 - 100,000', 50000, 100000),
              _buildPriceRangeChip('100,000 - 200,000', 100000, 200000),
              _buildPriceRangeChip('أكثر من 200,000', 200000, null),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRangeChip(String label, double? min, double? max) {
    final isSelected = _filters.minPrice == min && _filters.maxPrice == max;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _filters = _filters.copyWith(minPrice: min, maxPrice: max);
          } else {
            _filters = _filters.copyWith(minPrice: null, maxPrice: null);
          }
        });
      },
      selectedColor: AppColors.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primaryColor,
    );
  }

  Widget _buildRatingFilter() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التقييم',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: [4, 3, 2, 1].map((rating) {
              // final isSelected = _filters.minRating == rating.toDouble(); // غير مستخدم
              return ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Radio<double>(
                  value: rating.toDouble(),
                  groupValue: _filters.minRating,
                  onChanged: (value) {
                    setState(() {
                      _filters = _filters.copyWith(minRating: value);
                    });
                  },
                ),
                title: Row(
                  children: [
                    ...List.generate(5, (index) {
                      return Icon(
                        index < rating ? Icons.star : Icons.star_border,
                        color: AppColors.warning,
                        size: 20,
                      );
                    }),
                    const SizedBox(width: 8),
                    Text('$rating نجوم فأكثر'),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAvailabilityFilter() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingMedium),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التوفر والعروض',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          CheckboxListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('المنتجات المتوفرة فقط'),
            value: _filters.inStockOnly,
            onChanged: (value) {
              setState(() {
                _filters = _filters.copyWith(inStockOnly: value ?? false);
              });
            },
          ),
          CheckboxListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('المنتجات المخفضة فقط'),
            value: _filters.onSaleOnly,
            onChanged: (value) {
              setState(() {
                _filters = _filters.copyWith(onSaleOnly: value ?? false);
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ترتيب النتائج',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Column(
            children: SortOption.values.map((option) {
              return RadioListTile<SortOption>(
                contentPadding: EdgeInsets.zero,
                title: Text(_getSortOptionText(option)),
                value: option,
                groupValue: _filters.sortBy,
                onChanged: (value) {
                  setState(() {
                    _filters = _filters.copyWith(sortBy: value);
                  });
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _clearAllFilters,
                child: const Text('مسح الفلاتر'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _applyFilters,
                child: Text(
                  'تطبيق الفلاتر${_getActiveFiltersCount() > 0 ? ' (${_getActiveFiltersCount()})' : ''}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSortOptionText(SortOption option) {
    switch (option) {
      case SortOption.relevance:
        return 'الأكثر صلة';
      case SortOption.priceAsc:
        return 'السعر: من الأقل للأعلى';
      case SortOption.priceDesc:
        return 'السعر: من الأعلى للأقل';
      case SortOption.rating:
        return 'التقييم الأعلى';
      case SortOption.newest:
        return 'الأحدث';
      case SortOption.popular:
        return 'الأكثر شعبية';
    }
  }

  int _getActiveFiltersCount() {
    int count = 0;
    if (_filters.categoryId != null) count++;
    if (_filters.brand != null) count++;
    if (_filters.minPrice != null || _filters.maxPrice != null) count++;
    if (_filters.minRating != null) count++;
    if (_filters.inStockOnly) count++;
    if (_filters.onSaleOnly) count++;
    return count;
  }

  void _clearAllFilters() {
    setState(() {
      _filters = const SearchFilters();
    });
  }

  void _applyFilters() {
    widget.onFiltersApplied(_filters);
    Navigator.of(context).pop();
  }
}
