rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // دالة للتحقق من أن المستخدم مسجل دخول
    function isAuthenticated() {
      return request.auth != null;
    }

    // دالة للتحقق من أن المستخدم هو المالك للمورد
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }

    // دالة للتحقق من أن المستخدم مدير
    function isAdmin() {
      return request.auth != null &&
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    // دالة للتحقق من صحة البيانات المرسلة
    function isValidData() {
      return request.resource.data.keys().hasAll(['createdAt', 'updatedAt']) &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }

    // دالة للتحقق من حد الحجم للنصوص
    function isValidTextSize(field, maxLength) {
      return field in request.resource.data &&
             request.resource.data[field] is string &&
             request.resource.data[field].size() <= maxLength;
    }

    // دالة للتحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
      return email.matches('.*@.*\\..*');
    }

    // قواعد للمنتجات - قراءة للجميع، كتابة مؤقتة للجميع للاختبار
    match /products/{productId} {
      allow read: if true;
      allow create: if true; // مؤقت للاختبار
      allow update: if true; // مؤقت للاختبار
      allow delete: if isAdmin();
    }

    // قواعد للفئات - قراءة للجميع، كتابة مؤقتة للجميع للاختبار
    match /categories/{categoryId} {
      allow read: if true;
      allow create: if true; // مؤقت للاختبار
      allow update: if true; // مؤقت للاختبار
      allow delete: if isAdmin();
    }

    // قواعد للعلامات التجارية - قراءة للجميع، كتابة مؤقتة للجميع للاختبار
    match /brands/{brandId} {
      allow read: if true;
      allow create: if true; // مؤقت للاختبار
      allow update: if true; // مؤقت للاختبار
      allow delete: if isAdmin();
    }

    // قواعد للمستخدمين - كل مستخدم يمكنه قراءة وتعديل بياناته فقط
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow create: if isAuthenticated() &&
                    request.auth.uid == userId &&
                    isValidEmail(request.resource.data.email) &&
                    isValidTextSize('firstName', 50) &&
                    isValidTextSize('lastName', 50) &&
                    isValidData();
      allow update: if isOwner(userId) && isValidData();
      allow delete: if isAdmin(); // فقط المدراء يمكنهم حذف المستخدمين
    }

    // قواعد للمدراء - قراءة للمدراء فقط
    match /admins/{adminId} {
      allow read: if isAdmin();
      allow write: if false; // لا يمكن إضافة مدراء من التطبيق
    }

    // قواعد للطلبات - المستخدم يمكنه قراءة طلباته فقط، المدراء يمكنهم قراءة وتعديل جميع الطلبات
    match /orders/{orderId} {
      allow read: if isAuthenticated() &&
                  (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated() &&
                    request.resource.data.userId == request.auth.uid &&
                    isValidData() &&
                    'totalAmount' in request.resource.data &&
                    request.resource.data.totalAmount is number &&
                    request.resource.data.totalAmount > 0;
      allow update: if isAuthenticated() &&
                    (resource.data.userId == request.auth.uid || isAdmin()) &&
                    isValidData();
      allow delete: if isAdmin();
    }

    // قواعد للإشعارات - المستخدم يمكنه قراءة إشعاراته فقط
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() &&
                  (resource.data.userId == request.auth.uid || isAdmin());
      allow write: if isAdmin();
      allow create: if isAdmin();
    }

    // قواعد للعناوين - المستخدم يمكنه إدارة عناوينه فقط
    match /addresses/{addressId} {
      allow read, write: if isAuthenticated() &&
                         resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated();
    }

    // قواعد للمراجعات - قراءة للجميع، كتابة للمستخدمين المسجلين
    match /reviews/{reviewId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
                   (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isAuthenticated();
      allow delete: if isAdmin();
    }

    // قواعد للإحصائيات - قراءة للمدراء فقط
    match /statistics/{statId} {
      allow read, write: if isAdmin();
    }

    // قواعد للإعدادات العامة - قراءة للجميع، كتابة للمدراء فقط
    match /settings/{settingId} {
      allow read: if true;
      allow write: if isAdmin() && isValidData();
    }

    // قواعد للسلة - كل مستخدم يمكنه إدارة سلته فقط
    match /carts/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow delete: if isOwner(userId) || isAdmin();
    }

    // قواعد للمفضلة - كل مستخدم يمكنه إدارة مفضلته فقط
    match /favorites/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow delete: if isOwner(userId) || isAdmin();
    }

    // قواعد لسجل النشاط - للمدراء فقط
    match /activity_logs/{logId} {
      allow read: if isAdmin();
      allow write: if false; // يتم إنشاؤها تلقائياً بواسطة النظام
    }

    // قواعد للتقارير - للمدراء فقط
    match /reports/{reportId} {
      allow read, write: if isAdmin();
    }

    // منع الوصول لأي مجموعات أخرى غير محددة
    match /{document=**} {
      allow read, write: if false;
    }
  }
}