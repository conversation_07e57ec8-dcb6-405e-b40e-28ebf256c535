# دليل النشر الشامل - تطبيق VisionLens على Google Play

## 🚀 خطوات النشر خطوة بخطوة

### المرحلة 1: إعداد التوقيع الرقمي ✅

#### 1. إنشاء Keystore
```bash
# تشغيل السكريبت المرفق
create_keystore.bat
```

**معلومات مهمة:**
- كلمة المرور: `VisionLens2024!@#`
- اسم المفتاح: `visionlens-key`
- صالح لمدة 25 سنة

⚠️ **تحذير:** احتفظ بنسخة احتياطية من keystore وكلمة المرور في مكان آمن!

#### 2. التحقق من الإعدادات
- ✅ ملف `key.properties` تم إنشاؤه
- ✅ `build.gradle.kts` تم تحديثه
- ✅ قواعد ProGuard محسنة

### المرحلة 2: المحتوى القانوني ✅

#### الملفات المنشأة:
- ✅ `privacy_policy.md` - سياسة الخصوصية
- ✅ `terms_of_service.md` - شروط الاستخدام  
- ✅ `web/privacy_policy.html` - نسخة ويب
- ✅ `web/terms_of_service.html` - نسخة ويب

#### الروابط للـ Google Play:
- سياسة الخصوصية: `https://yourdomain.com/privacy_policy.html`
- شروط الاستخدام: `https://yourdomain.com/terms_of_service.html`

### المرحلة 3: المواد التسويقية

#### المطلوب إنشاؤه:
- [ ] لقطات شاشة (8 صور)
- [ ] Feature Graphic (1024x500)
- [ ] أيقونة عالية الدقة (512x512) ✅

#### الوصف جاهز:
- ✅ العنوان القصير
- ✅ الوصف القصير  
- ✅ الوصف الكامل
- ✅ الكلمات المفتاحية

## 🔧 بناء التطبيق للنشر

### الخطوة 1: إنشاء Keystore
```bash
# تشغيل السكريبت
create_keystore.bat
```

### الخطوة 2: بناء AAB للنشر
```bash
# تشغيل سكريبت البناء
build_for_release.bat
```

أو يدوياً:
```bash
flutter clean
flutter pub get
flutter build appbundle --release
```

### الخطوة 3: التحقق من الملف
الملف سيكون في: `build/app/outputs/bundle/release/app-release.aab`

## 📸 التقاط لقطات الشاشة

### الطريقة 1: استخدام المحاكي
```bash
# تشغيل التطبيق
flutter run --release

# التقاط لقطة شاشة
# استخدم أدوات المحاكي أو اختصار Ctrl+S
```

### الطريقة 2: استخدام جهاز حقيقي
- قم بتوصيل الجهاز
- فعّل وضع المطور
- استخدم أدوات التقاط الشاشة

### الطريقة 3: استخدام أدوات خارجية
- **scrcpy** - لعرض شاشة الهاتف على الكمبيوتر
- **ADB** - لالتقاط لقطات عبر سطر الأوامر

## 🎨 إنشاء Feature Graphic

### الأدوات المقترحة:
1. **Canva** - سهل ومجاني
2. **Figma** - للتصميم المتقدم
3. **Photoshop** - للمحترفين

### العناصر المطلوبة:
- شعار VisionLens
- صور نظارات وعدسات
- نص جذاب بالعربية
- ألوان العلامة التجارية

### القالب المقترح:
```
[شعار VisionLens]     [صور منتجات]
    
    متجر النظارات والعدسات الأول
         توصيل سريع - جودة مضمونة
```

## 📋 قائمة التحقق النهائية

### الملفات التقنية
- [x] keystore تم إنشاؤه
- [x] build.gradle.kts محدث
- [x] proguard-rules.pro محسن
- [ ] AAB تم بناؤه بنجاح

### المحتوى القانوني
- [x] سياسة الخصوصية
- [x] شروط الاستخدام
- [ ] رفع الملفات لموقع الويب
- [ ] التحقق من الروابط

### المواد التسويقية
- [ ] 8 لقطات شاشة للهاتف
- [ ] Feature Graphic (1024x500)
- [x] وصف التطبيق
- [x] الكلمات المفتاحية

### إعدادات Google Play Console
- [ ] إنشاء التطبيق في Console
- [ ] رفع AAB الأول
- [ ] إضافة جميع المواد
- [ ] مراجعة المعلومات
- [ ] إرسال للمراجعة

## ⚠️ تحذيرات مهمة

1. **لا تفقد keystore أبداً** - بدونه لن تتمكن من تحديث التطبيق
2. **اختبر التطبيق جيداً** قبل النشر
3. **راجع سياسات Google Play** للتأكد من التوافق
4. **احتفظ بنسخ احتياطية** من جميع الملفات المهمة

## 📞 الدعم

إذا واجهت أي مشاكل، راجع:
- [دليل Flutter للنشر](https://docs.flutter.dev/deployment/android)
- [دليل Google Play Console](https://support.google.com/googleplay/android-developer)
- [دليل Firebase](https://firebase.google.com/docs)
