import 'package:shared_preferences/shared_preferences.dart';
import 'models/product.dart';
import 'models/user_simple.dart';
import 'models/category.dart';
import 'app_properties.dart';

class ApiService {
  static const String baseUrl = AppConstants.baseUrl;
  static const Duration timeoutDuration = Duration(seconds: 30);

  // حفظ التوكن في التخزين المحلي
  static Future<void> _saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userTokenKey, token);
  }

  // إزالة التوكن من التخزين المحلي
  static Future<void> _removeAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userTokenKey);
  }

  // ==================== خدمات المصادقة ====================

  // تسجيل الدخول
  static Future<AuthResponse> login(String email, String password) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // محاكاة تسجيل الدخول
      if (email.isNotEmpty && password.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: '1',
          email: email,
          firstName: 'مستخدم',
          lastName: 'VisionLens',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في تسجيل الدخول');
    }
  }

  // التسجيل
  static Future<AuthResponse> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // محاكاة التسجيل
      if (email.isNotEmpty &&
          password.isNotEmpty &&
          firstName.isNotEmpty &&
          lastName.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
          isPhoneVerified: phone != null,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(message: 'يرجى ملء جميع الحقول المطلوبة');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في التسجيل');
    }
  }

  // إرسال رمز التحقق
  static Future<void> sendOTP(String phone) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال OTP
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رمز التحقق');
    }
  }

  // التحقق من رمز OTP
  static Future<void> verifyOTP(String phone, String otp) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة التحقق من OTP
      if (otp != '1234') {
        throw ApiException(message: 'رمز التحقق غير صحيح');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'فشل في التحقق من الرمز');
    }
  }

  // نسيان كلمة المرور
  static Future<void> forgotPassword(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال رابط إعادة تعيين كلمة المرور
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رابط إعادة التعيين');
    }
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await _removeAuthToken();
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج
      await _removeAuthToken();
    }
  }

  // ==================== خدمات المنتجات ====================

  // جلب المنتجات
  static Future<List<Product>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      // إنشاء منتجات تجريبية
      return _generateMockProducts(limit);
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات');
    }
  }

  // جلب منتج بالمعرف
  static Future<Product> getProductById(String id) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      return _generateMockProducts(1).first;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب تفاصيل المنتج');
    }
  }

  // جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));

      return _generateMockProducts(5);
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات المميزة');
    }
  }

  // ==================== خدمات الفئات ====================

  // جلب الفئات
  static Future<List<Category>> getCategories() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      return PredefinedCategories.getCategories();
    } catch (e) {
      return PredefinedCategories.getCategories();
    }
  }

  // ==================== خدمات المستخدم ====================

  // جلب بيانات المستخدم الحالي
  static Future<User> getCurrentUser() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final user = User(
        id: '1',
        email: '<EMAIL>',
        firstName: 'مستخدم',
        lastName: 'VisionLens',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب بيانات المستخدم');
    }
  }

  // تحديث بيانات المستخدم
  static Future<User> updateUser(Map<String, dynamic> userData) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      final user = User(
        id: userData['id'] ?? '1',
        email: userData['email'] ?? '<EMAIL>',
        firstName: userData['firstName'] ?? 'مستخدم',
        lastName: userData['lastName'] ?? 'VisionLens',
        phone: userData['phone'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث بيانات المستخدم');
    }
  }

  // إنشاء منتجات تجريبية
  static List<Product> _generateMockProducts(int count) {
    return List.generate(count, (index) {
      return Product(
        id: 'product_$index',
        name: 'نظارة VisionLens ${index + 1}',
        description:
            'نظارة عالية الجودة مع تصميم عصري وراحة فائقة للاستخدام اليومي',
        price: 75000.0 + (index * 25000), // بالدينار العراقي
        originalPrice: index % 3 == 0 ? 100000.0 + (index * 25000) : null,
        image: 'assets/images/product_$index.jpg',
        images: ['assets/images/product_$index.jpg'],
        categoryId: 'eyeglasses',
        categoryName: 'نظارات طبية',
        rating: 4.0 + (index % 2 * 0.5),
        reviewsCount: 10 + (index * 5),
        inStock: true,
        stockQuantity: 50,
        specifications: {
          'العلامة التجارية': 'VisionLens',
          'المادة': 'أسيتات عالي الجودة',
          'اللون': 'أسود',
          'المقاس': 'متوسط',
        },
        type: ProductType.eyeglasses,
        brand: 'VisionLens',
        material: 'أسيتات',
        color: 'أسود',
        size: 'متوسط',
        availableColors: ['أسود', 'بني', 'أزرق'],
        availableSizes: ['صغير', 'متوسط', 'كبير'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isNew: index < 3,
        isFeatured: index % 2 == 0,
        isOnSale: index % 3 == 0,
        discountPercentage: index % 3 == 0 ? 25.0 : null,
      );
    });
  }
}

// استجابة المصادقة
class AuthResponse {
  final User user;
  final String token;

  AuthResponse({required this.user, required this.token});
}

// استثناء API
class ApiException implements Exception {
  final int? statusCode;
  final String message;

  ApiException({this.statusCode, required this.message});

  @override
  String toString() => message;
}
