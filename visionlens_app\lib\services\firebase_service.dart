import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/product.dart';
import '../models/user_simple.dart' as user_model;
import '../models/order.dart' as order_model;
import '../models/category.dart';

class FirebaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collections
  static const String _usersCollection = 'users';
  static const String _productsCollection = 'products';
  static const String _categoriesCollection = 'categories';
  static const String _ordersCollection = 'orders';
  static const String _reviewsCollection = 'reviews';
  static const String _cartCollection = 'cart';
  static const String _wishlistCollection = 'wishlist';

  // ==================== المنتجات ====================

  // الحصول على جميع المنتجات
  static Future<List<Product>> getAllProducts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_productsCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  // الحصول على منتج بالمعرف
  static Future<Product?> getProductById(String productId) async {
    try {
      final doc = await _firestore
          .collection(_productsCollection)
          .doc(productId)
          .get();

      if (doc.exists) {
        return Product.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      print('خطأ في جلب المنتج: $e');
      return null;
    }
  }

  // البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    try {
      final querySnapshot = await _firestore
          .collection(_productsCollection)
          .where('isActive', isEqualTo: true)
          .where('searchKeywords', arrayContains: query.toLowerCase())
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('خطأ في البحث: $e');
      return [];
    }
  }

  // الحصول على منتجات بفئة معينة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_productsCollection)
          .where('categoryId', isEqualTo: categoryId)
          .where('isActive', isEqualTo: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('خطأ في جلب منتجات الفئة: $e');
      return [];
    }
  }

  // إضافة منتج جديد (للإدارة)
  static Future<String?> addProduct(Product product) async {
    try {
      final docRef = await _firestore
          .collection(_productsCollection)
          .add(product.toJson());
      return docRef.id;
    } catch (e) {
      print('خطأ في إضافة المنتج: $e');
      return null;
    }
  }

  // تحديث منتج
  static Future<bool> updateProduct(
    String productId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _firestore
          .collection(_productsCollection)
          .doc(productId)
          .update(updates);
      return true;
    } catch (e) {
      print('خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  // ==================== الفئات ====================

  // الحصول على جميع الفئات
  static Future<List<Category>> getAllCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_categoriesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      return querySnapshot.docs
          .map((doc) => Category.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      print('خطأ في جلب الفئات: $e');
      return [];
    }
  }

  // ==================== المستخدمين ====================

  // الحصول على بيانات المستخدم الحالي
  static Future<user_model.User?> getCurrentUser() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final doc = await _firestore
          .collection(_usersCollection)
          .doc(currentUser.uid)
          .get();

      if (doc.exists) {
        return user_model.User.fromJson({...doc.data()!, 'id': doc.id});
      }
      return null;
    } catch (e) {
      print('خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  // إنشاء مستخدم جديد
  static Future<bool> createUser(user_model.User user) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(user.id)
          .set(user.toJson());
      return true;
    } catch (e) {
      print('خطأ في إنشاء المستخدم: $e');
      return false;
    }
  }

  // تحديث بيانات المستخدم
  static Future<bool> updateUser(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _firestore.collection(_usersCollection).doc(userId).update(updates);
      return true;
    } catch (e) {
      print('خطأ في تحديث المستخدم: $e');
      return false;
    }
  }

  // ==================== السلة ====================

  // الحصول على سلة المستخدم
  static Future<List<Map<String, dynamic>>> getUserCart(String userId) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_cartCollection)
          .get();

      return doc.docs.map((doc) => {...doc.data(), 'id': doc.id}).toList();
    } catch (e) {
      print('خطأ في جلب السلة: $e');
      return [];
    }
  }

  // إضافة عنصر للسلة
  static Future<bool> addToCart(
    String userId,
    Map<String, dynamic> cartItem,
  ) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_cartCollection)
          .add(cartItem);
      return true;
    } catch (e) {
      print('خطأ في إضافة للسلة: $e');
      return false;
    }
  }

  // تحديث عنصر في السلة
  static Future<bool> updateCartItem(
    String userId,
    String cartItemId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_cartCollection)
          .doc(cartItemId)
          .update(updates);
      return true;
    } catch (e) {
      print('خطأ في تحديث السلة: $e');
      return false;
    }
  }

  // حذف عنصر من السلة
  static Future<bool> removeFromCart(String userId, String cartItemId) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_cartCollection)
          .doc(cartItemId)
          .delete();
      return true;
    } catch (e) {
      print('خطأ في حذف من السلة: $e');
      return false;
    }
  }

  // مسح السلة
  static Future<bool> clearCart(String userId) async {
    try {
      final batch = _firestore.batch();
      final cartItems = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_cartCollection)
          .get();

      for (final doc in cartItems.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('خطأ في مسح السلة: $e');
      return false;
    }
  }

  // ==================== المفضلة ====================

  // الحصول على مفضلة المستخدم
  static Future<List<Map<String, dynamic>>> getUserWishlist(
    String userId,
  ) async {
    try {
      final doc = await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_wishlistCollection)
          .get();

      return doc.docs.map((doc) => {...doc.data(), 'id': doc.id}).toList();
    } catch (e) {
      print('خطأ في جلب المفضلة: $e');
      return [];
    }
  }

  // إضافة للمفضلة
  static Future<bool> addToWishlist(String userId, String productId) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_wishlistCollection)
          .doc(productId)
          .set({
            'productId': productId,
            'addedAt': FieldValue.serverTimestamp(),
          });
      return true;
    } catch (e) {
      print('خطأ في إضافة للمفضلة: $e');
      return false;
    }
  }

  // حذف من المفضلة
  static Future<bool> removeFromWishlist(
    String userId,
    String productId,
  ) async {
    try {
      await _firestore
          .collection(_usersCollection)
          .doc(userId)
          .collection(_wishlistCollection)
          .doc(productId)
          .delete();
      return true;
    } catch (e) {
      print('خطأ في حذف من المفضلة: $e');
      return false;
    }
  }

  // ==================== الطلبات ====================

  // إنشاء طلب جديد
  static Future<String?> createOrder(order_model.Order order) async {
    try {
      final docRef = await _firestore
          .collection(_ordersCollection)
          .add(order.toJson());
      return docRef.id;
    } catch (e) {
      print('خطأ في إنشاء الطلب: $e');
      return null;
    }
  }

  // الحصول على طلبات المستخدم
  static Future<List<order_model.Order>> getUserOrders(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_ordersCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map(
            (doc) => order_model.Order.fromJson({...doc.data(), 'id': doc.id}),
          )
          .toList();
    } catch (e) {
      print('خطأ في جلب الطلبات: $e');
      return [];
    }
  }

  // تحديث حالة الطلب
  static Future<bool> updateOrderStatus(
    String orderId,
    order_model.OrderStatus status,
  ) async {
    try {
      await _firestore.collection(_ordersCollection).doc(orderId).update({
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  // ==================== التقييمات ====================

  // إضافة تقييم
  static Future<bool> addReview(
    String productId,
    String userId,
    double rating,
    String comment,
  ) async {
    try {
      await _firestore.collection(_reviewsCollection).add({
        'productId': productId,
        'userId': userId,
        'rating': rating,
        'comment': comment,
        'createdAt': FieldValue.serverTimestamp(),
        'isApproved': false,
      });
      return true;
    } catch (e) {
      print('خطأ في إضافة التقييم: $e');
      return false;
    }
  }

  // الحصول على تقييمات المنتج
  static Future<List<Map<String, dynamic>>> getProductReviews(
    String productId,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection(_reviewsCollection)
          .where('productId', isEqualTo: productId)
          .where('isApproved', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => {...doc.data(), 'id': doc.id})
          .toList();
    } catch (e) {
      print('خطأ في جلب التقييمات: $e');
      return [];
    }
  }

  // ==================== رفع الملفات ====================

  // رفع صورة
  static Future<String?> uploadImage(String path, List<int> imageBytes) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = ref.putData(Uint8List.fromList(imageBytes));
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      print('خطأ في رفع الصورة: $e');
      return null;
    }
  }

  // ==================== الإحصائيات ====================

  // الحصول على إحصائيات المنتجات
  static Future<Map<String, int>> getProductStats() async {
    try {
      final products = await _firestore.collection(_productsCollection).get();
      final categories = await _firestore
          .collection(_categoriesCollection)
          .get();
      final orders = await _firestore.collection(_ordersCollection).get();
      final users = await _firestore.collection(_usersCollection).get();

      return {
        'products': products.size,
        'categories': categories.size,
        'orders': orders.size,
        'users': users.size,
      };
    } catch (e) {
      print('خطأ في جلب الإحصائيات: $e');
      return {};
    }
  }

  // ==================== المساعدة ====================

  // التحقق من الاتصال
  static Future<bool> checkConnection() async {
    try {
      await _firestore.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  // تهيئة البيانات التجريبية
  static Future<void> initializeSampleData() async {
    try {
      // سيتم تنفيذ هذا لاحقاً
      print('تهيئة البيانات التجريبية...');
    } catch (e) {
      print('خطأ في تهيئة البيانات: $e');
    }
  }
}
